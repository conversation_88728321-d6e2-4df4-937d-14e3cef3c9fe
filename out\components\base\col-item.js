var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
let WexColItem = class WexColItem extends LitElement {
    render() {
        const width = this.width ?? "auto";
        const justifyContent = this.justifyContent ?? "space-evenly";
        const alignItems = this.alignItems ?? "stretch";
        const flex = this.flex ?? "1";
        return html `
      <div
        class="wrapper"
        style="width: ${width}; justify-content: ${justifyContent}; align-items: ${alignItems}; flex: ${flex};"
      >
        <slot></slot>
      </div>
    `;
    }
};
// removed height 100% here to try and fix issues with content stretching
WexColItem.styles = css `
    :host {
      display: flex;
      flex: 1;
    }

    .wrapper {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 40px;
      height: 100%;
    }
    .wrapper > *:not(:last-child) {
      margin-bottom: 0.5rem;
    }
  `;
__decorate([
    property({ type: String })
], WexColItem.prototype, "alignItems", void 0);
__decorate([
    property({ type: String })
], WexColItem.prototype, "justifyContent", void 0);
__decorate([
    property({ type: String })
], WexColItem.prototype, "flex", void 0);
__decorate([
    property({ type: String })
], WexColItem.prototype, "width", void 0);
WexColItem = __decorate([
    customElement("wex-col-item")
], WexColItem);
export { WexColItem };
//# sourceMappingURL=col-item.js.map