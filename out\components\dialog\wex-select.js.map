{"version": 3, "file": "wex-select.js", "sourceRoot": "", "sources": ["../../../src/components/dialog/wex-select.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAExC,OAAO,kBAAkB,CAAC;AAE1B,8CAA8C;AAC9C,8CAA8C;AAC9C,6CAA6C;AAC7C,8CAA8C;AAGvC,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,UAAU;IAAxC;;QACuB,mBAAc,GAAQ,IAAI,CAAC;QAC9C,UAAK,GAAQ,EAAE,CAAC;QAChB,WAAM,GAAQ,EAAE,CAAC;QACjB,iBAAY,GAAQ,IAAI,CAAC;QACzB,YAAO,GAAU,EAAE,CAAC;QAC7B,+BAA+B;QAC/B,gCAAgC;QACvB,aAAQ,GAAU,EAAE,CAAC;QACrB,eAAU,GAAU,EAAE,CAAC;QA8IxB,cAAS,GAAG,KAAK,IAAI,EAAE;YAC7B,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC9C,oEAAoE;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC;YAC5D,+BAA+B;YAE/B,wEAAwE;YAExE,kEAAkE;YAClE,wDAAwD;YACxD,gEAAgE;YAChE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,eAAe,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEvD,MAAM,IAAI,GAAG;gBACX,GAAG,MAAM;gBACT,QAAQ;gBACR,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,QAAQ;gBAChC,0BAA0B;gBAC1B,iCAAiC;gBACjC,gCAAgC;aACjC,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YACtC,6CAA6C;YAC7C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;YACrC,IAAI;YACJ,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC9B,iDAAiD;YACjD,uDAAuD;YAEvD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IAgCJ,CAAC;IA/MC,MAAM,KAAK,MAAM;QACf,MAAM;QACN,4BAA4B;QAC5B,IAAI;QACJ,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4FT,CAAC;IACJ,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAEO,IAAI,CAAC,KAAU;QACrB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC;QACjD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,sBAAsB,CAAC;IACrD,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,sBAAsB,CAAC;QACnD,oDAAoD;QAEpD,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACrE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAC7C,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAC7B,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAC/C,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAC9B,CAAC;QACJ,CAAC;QACD,4DAA4D;QAC5D,gEAAgE;IAClE,CAAC;IAuCO,qBAAqB,CAAC,CAAc;QAC1C,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACzE,CAAC;IAES,MAAM;QACd,yEAAyE;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAA;;;gBAGT,CAAC,CAAC,IAAI,CAAC,cAAc;qBAChB,IAAI,CAAC,SAAS;;;;qBAId,IAAI,CAAC,cAAc,EAAE,UAAU,IAAI,WAAW;;;;sBAI7C,IAAI,CAAC,QAAQ;wBACX,IAAI,CAAC,UAAU;+BACR,IAAI,CAAC,qBAAqB;;;KAGpD,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAzN6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;uDAA4B;AAC9C;IAAR,KAAK,EAAE;8CAAiB;AAChB;IAAR,KAAK,EAAE;+CAAkB;AACjB;IAAR,KAAK,EAAE;qDAA0B;AACzB;IAAR,KAAK,EAAE;gDAAqB;AAGpB;IAAR,KAAK,EAAE;iDAAsB;AACrB;IAAR,KAAK,EAAE;mDAAwB;AATrB,eAAe;IAD3B,aAAa,CAAC,mBAAmB,CAAC;GACtB,eAAe,CA0N3B", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"../common/dialog\";\r\n\r\n// import \"@ui5/webcomponents/dist/Button.js\";\r\n// import \"@ui5/webcomponents/dist/Dialog.js\";\r\n// import \"@ui5/webcomponents/dist/Label.js\";\r\n// import \"@polymer/iron-icons/iron-icons.js\";\r\n\r\n@customElement(\"wex-dialog-select\")\r\nexport class WexDialogSelect extends LitElement {\r\n  @property({ type: Object }) dialogOpenData: any = null;\r\n  @state() state: any = {};\r\n  @state() string: any = {};\r\n  @state() subscription: any = null;\r\n  @state() actions: any[] = [];\r\n  // @state() cbMsg: string = \"\";\r\n  // @state() header: string = \"\";\r\n  @state() selected: any[] = [];\r\n  @state() unselected: any[] = [];\r\n\r\n  static get styles() {\r\n    // * {\r\n    //   box-sizing: border-box;\r\n    // }\r\n    return css`\r\n      .italic {\r\n        font-style: italic;\r\n      }\r\n\r\n      #dialog {\r\n        width: min(90vw, 600px);\r\n      }\r\n      .dialog-content {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        padding: 1rem;\r\n        height: min(50vh, 300px);\r\n        color: var(--font-color);\r\n      }\r\n      .dialog-content * {\r\n        color: var(--font-color);\r\n      }\r\n      .dialog-content > *:not(:last-child) {\r\n        margin-right: 0.5rem;\r\n      }\r\n\r\n      ul {\r\n        flex-grow: 1;\r\n        margin: 0;\r\n        padding: 0;\r\n        list-style: none;\r\n        border: 1px solid var(--clr-gray-light);\r\n        overflow-y: auto;\r\n        max-height: calc(2.25rem * 8);\r\n        height: 30vh;\r\n      }\r\n      li {\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 1rem;\r\n        height: 2.25rem;\r\n        border-bottom: 1px solid var(--clr-gray-light);\r\n      }\r\n      li > span {\r\n        flex-grow: 1;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n      li > .icon-container {\r\n        position: relative;\r\n        width: 1.5rem;\r\n        height: 1.5rem;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 8px;\r\n      }\r\n      li > .icon-container:hover {\r\n        background: var(--clr-white);\r\n      }\r\n      li > *:not(:last-child) {\r\n        margin-right: 1rem;\r\n      }\r\n      li[active] {\r\n        background: var(--row-selected-background);\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n      li:not([active]):hover {\r\n        background: var(--clr-gray-ultra-light);\r\n      }\r\n\r\n      .select-column {\r\n        flex-grow: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n        width: 100%;\r\n      }\r\n      .select-column > span {\r\n        margin-bottom: 0.5rem;\r\n      }\r\n\r\n      .select-actions {\r\n        display: flex;\r\n        flex-direction: column;\r\n      }\r\n      .select-actions > *:not(:last-child) {\r\n        margin-bottom: 0.5rem;\r\n      }\r\n      .select-actions > * {\r\n        cursor: pointer;\r\n      }\r\n    `;\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this.init(state);\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  private init(state: any) {\r\n    this.actions = state.menus.dialog_select_actions;\r\n    this.dialogOpenData = state.wex_select_dialog_open;\r\n  }\r\n\r\n  private stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.dialogOpenData = state.wex_select_dialog_open;\r\n    // this.headerText = this.dialogOpenData.headerText;\r\n\r\n    if (!!this.dialogOpenData) {\r\n      console.log(\"stateChange: this.dialogOpenData\", this.dialogOpenData);\r\n      this.selected = this.dialogOpenData.data.filter(\r\n        (item: any) => item.selected\r\n      );\r\n      this.unselected = this.dialogOpenData.data.filter(\r\n        (item: any) => !item.selected\r\n      );\r\n    }\r\n    // console.log(\"stateChange: this.selected\", this.selected);\r\n    // console.log(\"stateChange: this.unselected\", this.unselected);\r\n  }\r\n\r\n  private onConfirm = async () => {\r\n    console.log(\"onConfirm: this.dialogOpenData\", this.dialogOpenData);\r\n    const pubDefId = this.dialogOpenData.pubDefId;\r\n    // const items = this.selected.map((item) => item.langId).join(\",\");\r\n    const pubDef = this.dialogOpenData.pubDef;\r\n    const updateTargetKey = this.dialogOpenData.updateTargetKey;\r\n    // const items = this.selected;\r\n\r\n    // const { lstLanguages, lstOutputFormats, lstPubContentDtos } = pubDef;\r\n\r\n    // console.log(\"onConfirm: lstPubContentDtos\", lstPubContentDtos);\r\n    // console.log(\"onConfirm: lstLanguages\", lstLanguages);\r\n    // console.log(\"onConfirm: lstOutputFormats\", lstOutputFormats);\r\n    console.log(\"onConfirm: pubDef\", pubDef);\r\n    console.log(\"onConfirm: updateTargetKey\", updateTargetKey);\r\n    console.log(\"onConfirm: this.selected\", this.selected);\r\n\r\n    const body = {\r\n      ...pubDef,\r\n      pubDefId,\r\n      [updateTargetKey]: this.selected,\r\n      // pubLangs: lstLanguages,\r\n      // pubContent: lstPubContentDtos,\r\n      // pubOutputs: lstOutputFormats,\r\n    };\r\n\r\n    body[updateTargetKey] = this.selected;\r\n    // if (this.dialogOpenData.updateTargetKey) {\r\n    console.log(\"onConfirm: body\", body);\r\n    // }\r\n    await wexlib.editPubDef(body);\r\n    // await this.dialogOpenData.closeCallback(body);\r\n    // await this.dialogOpenData.refreshCallback(pubDefId);\r\n\r\n    return true;\r\n  };\r\n\r\n  private handlePickListUpdated(e: CustomEvent) {\r\n    console.log(\"handlePickListUpdated\");\r\n    this.selected = e.detail.selected;\r\n    this.unselected = e.detail.unselected;\r\n    console.log(\"handlePickListUpdated: this.selected\", this.selected);\r\n    console.log(\"handlePickListUpdated: this.unselected\", this.unselected);\r\n  }\r\n\r\n  protected render() {\r\n    // dialogOpenStateProperty=${this.dialogOpenData.dialogOpenStateProperty}\r\n    const template = html`\r\n      <wex-dialog\r\n        id=\"dialog\"\r\n        .open=${!!this.dialogOpenData}\r\n        .onConfirm=${this.onConfirm}\r\n        confirmLabel=\"Ok\"\r\n        cancelLabel=\"Cancel\"\r\n        dialogOpenStateProperty=\"wex_select_dialog_open\"\r\n        headerText=${this.dialogOpenData?.headerText || \"No Header\"}\r\n        width=\"60%\"\r\n      >\r\n        <wex-list-picker\r\n          .selected=${this.selected}\r\n          .unselected=${this.unselected}\r\n          @pick-list-updated=${this.handlePickListUpdated}\r\n        ></wex-list-picker>\r\n      </wex-dialog>\r\n    `;\r\n    return template;\r\n  }\r\n}\r\n"]}