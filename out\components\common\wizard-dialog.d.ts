import { LitElement } from "lit-element";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/FileUploader.js";
import "../wex-table";
import "./list-builder";
import "@ui5/webcomponents/dist/RadioButton";
import "./wizard-stage-tabs";
import "./dialog";
export declare class WexWizardDialog extends LitElement {
    open: boolean;
    headerText: string;
    width: string;
    height: string;
    dialogOpenStateProperty: string | null;
    stages: Record<string, any>;
    initialStage: string;
    mode: string;
    stage: string;
    static get styles(): import("lit-element").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    protected firstUpdated(): void;
    protected updated(changedProperties: Map<string | number | symbol, unknown>): void;
    private init;
    private setHeaderText;
    private setConfirmLabel;
    private setCancelLabel;
    private renderStageTabs;
    private onConfirm;
    private onCancel;
    protected render(): import("lit-element").TemplateResult;
}
