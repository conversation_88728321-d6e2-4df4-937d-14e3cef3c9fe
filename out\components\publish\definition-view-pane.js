var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// @ts-ignore
import * as wexlib from "lib/wexlib.js";
import "../base/col-item";
import "../base/row-item";
let WexDefinitionViewPane = class WexDefinitionViewPane extends LitElement {
    constructor() {
        super(...arguments);
        this.selectedProjectId = null;
        this.selectedPubDefId = null;
        this.pubDef = null;
        this.isLoading = false;
        this.state = storeInstance.state;
        this.string = {};
        this.subscription = null;
    }
    static get styles() {
        return css `
      :host {
        display: flex;
        flex: 1;
        width: 100%;
        overflow: hidden;
        max-height: 70vh;
      }

      .loading {
        position: absolute;
        width: inherit;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(var(--clr-rgb-gray-ultra-light), 0.7);
        z-index: 10;
      }

      .dev-view {
        flex-direction: column;
        width: 70%;
        border-radius: 5px;
        border: 1px solid #556b81;
        padding: 1rem;
        overflow: auto;
        height: 100%;
      }

      .dev-view > *:not(:last-child) {
        margin-bottom: 1rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
      }

      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .bold {
        font-style: bold;
      }
      .italic {
        font-style: italic;
      }

      .pointer {
        cursor: pointer;
      }

      .action-icon,
      .more-icon {
        width: 1.5rem;
        height: 1.5rem;
        padding: 0.25rem;
        border-radius: 0.25rem;
      }
      .action-icon:hover {
        background-color: var(--clr-primary-ultra-light);
      }

      ui5-input {
        width: 100%;
      }
    `;
        // .more-icon:hover {
        //   background-color: var(--clr-white);
        // }
    }
    connectedCallback() {
        super.connectedCallback();
        // this.state = storeInstance.state;
        // this.string = storeInstance.state[storeInstance.state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this.init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    async stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        // refresh pubDef when dialog goes null ie) it was closed
        if (state.wex_select_dialog_open === null) {
            console.log("stateChange: wex_select_dialog_open is null");
            this.pubDef = await wexlib.getPubDef(this.selectedPubDefId);
        }
    }
    async updated(changedProps) {
        if (changedProps.has("selectedPubDefId")) {
            if (this.selectedPubDefId) {
                this.pubDef = await wexlib.getPubDef(this.selectedPubDefId);
            }
            else {
                this.pubDef = null;
            }
        }
    }
    init() { }
    renderLoading() {
        const loading = html `
      <div class="loading">
        <ui5-busy-indicator active> </ui5-busy-indicator>
      </div>
    `;
        if (this.isLoading)
            return loading;
        return null;
    }
    closePubDef() {
        this.selectedPubDefId = null;
        this.pubDef = null;
    }
    // change handler for pubdef name // TODO
    async handleChange(type = null, e = null) {
        // try {
        //   if (!type || !e) return;
        //   if (type == "pubdef-name") {
        //     const name = e.currentTarget?.value || "";
        //     await wexlib.setPubDefName(this.pubDef?.pubDefId, name);
        //   }
        //   this.pubDef = await wexlib.getPubDef(item.pubDefId);
        //   this.requestUpdate();
        // } catch {}
    }
    async editPubContent() {
        console.log("editPubContent");
        if (!this.selectedProjectId)
            return;
        const curr = this.pubDef?.lstPubContentDtos;
        const res = await wexlib.getProjPubContents(this.selectedProjectId);
        res.forEach((x) => {
            const exist = curr.find((y) => y.pubContentId == x.pubContentId);
            if (exist)
                x.selected = true;
            return x;
        });
        storeInstance.dispatch("setState", {
            property: "wex_select_dialog_open",
            value: {
                pubDefId: this.selectedPubDefId,
                pubDef: this.pubDef, // pass entire pubDef instead of just ID
                headerText: "Edit Publication Content",
                updateTargetKey: "pubContent",
                data: res,
                // closeCallback: (closeData) => wexlib.setPubDefPubContents(closeData),
                closeCallback: (closeData) => wexlib.editPubDef(closeData),
                refreshCallback: async (id) => {
                    const updated = await wexlib.getPubDef(id);
                    this.pubDef = updated;
                },
            },
        });
    }
    async editPubLangs() {
        console.log("editPubLangs");
        if (!this.selectedProjectId)
            return;
        const curr = this.pubDef?.lstLanguages;
        curr.forEach((x) => (x.name = x.description));
        const res = await wexlib.getProjLanguages(this.selectedProjectId);
        res.forEach((x) => {
            const exist = curr.find((y) => y.langId == x.langId);
            if (exist)
                x.selected = true;
            x.name = x.description;
            return x;
        });
        storeInstance.dispatch("setState", {
            property: "wex_select_dialog_open",
            value: {
                pubDefId: this.selectedPubDefId,
                pubDef: this.pubDef,
                headerText: "Edit Languages",
                updateTargetKey: "pubLangs",
                data: res,
                // closeCallback: (closeData: any) => wexlib.setPubDefLanguages(closeData),
                closeCallback: (closeData) => wexlib.editPubDef(closeData),
                refreshCallback: async (id) => {
                    const updated = await wexlib.getPubDef(id);
                    this.pubDef = updated;
                },
            },
        });
    }
    async editPubOutputs() {
        const res = await wexlib.getPubEngineOutputs();
        const curr = this.pubDef?.lstOutputFormats;
        res.forEach((x) => {
            const exist = curr.find((y) => y.publishEngineId === x.publishEngineId &&
                y.outputTypeId === x.outputTypeId);
            if (exist)
                x.selected = true;
            return x;
        });
        storeInstance.dispatch("setState", {
            property: "wex_select_dialog_open",
            value: {
                pubDefId: this.selectedPubDefId,
                pubDef: this.pubDef,
                headerText: "Edit Output Formats",
                updateTargetKey: "pubOutputs",
                data: res,
                closeCallback: (closeData) => wexlib.setPubDefEngineOutputs(closeData),
                refreshCallback: async (id) => {
                    const updated = await wexlib.getPubDef(id);
                    this.pubDef = updated;
                },
            },
        });
    }
    renderCloseButton() {
        return html ` <wex-row-item justifyContent="right">
      <iron-icon
        icon="clear"
        class="pointer action-icon"
        title="${this.string["_close"]}"
        @click="${this.closePubDef.bind(this)}"
      ></iron-icon>
    </wex-row-item>`;
    }
    renderProjectName() {
        return html `
      <wex-row-item alignItems="flex-start" height="7rem">
        <wex-col-item>
          <h3>${this.string["_project_name"]}</h3>
          <ui5-input value="${this.pubDef?.projectName}" readonly></ui5-input>
        </wex-col-item>
        <wex-col-item>
          <h3>${this.string["_pubdef_name"]}</h3>
          <ui5-input
            readonly
            value="${this.pubDef?.name}"
            @change="${this.handleChange.bind(this, "pubdef-name")}"
          ></ui5-input>
        </wex-col-item>
      </wex-row-item>
    `;
    }
    renderContent() {
        // title="${this.string["_edit_x"](this.string["_pub_content"])}"
        return html `
      <wex-row-item justifyContent="space-between">
        <h3>${this.string["_pub_content"]}</h3>
        <iron-icon
          title="Publication Content"
          icon="create"
          class="pointer action-icon"
          @click="${this.editPubContent.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this.pubDef?.lstPubContentDtos.length > 0
            ? this.pubDef?.lstPubContentDtos.map((x) => html ` <li><span>${x.name}</span></li> `)
            : html `
              <li>
                <span class="italic"> ${this.string["_no_item"]} </span>
              </li>
            `}
      </ul>
    `;
    }
    renderLanguages() {
        // title="${this.string["_edit_x"](this.string["_languages"])}"
        return html ` <wex-row-item justifyContent="space-between">
        <h3>${this.string["_languages"]}</h3>
        <iron-icon
          icon="create"
          class="pointer action-icon"
          title="Languages"
          @click="${this.editPubLangs.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this.pubDef?.lstLanguages.length > 0
            ? this.pubDef?.lstLanguages.map((x) => html ` <li><span>${x.description}</span></li> `)
            : html `
              <li>
                <span class="italic"> ${this.string["_no_item"]} </span>
              </li>
            `}
      </ul>
      .`;
    }
    renderOutputs() {
        // title="${this.string["_edit_x"](this.string["_output_format"])}"
        return html `
      <wex-row-item justifyContent="space-between">
        <h3>${this.string["_output_format"]}</h3>
        <iron-icon
          icon="create"
          class="pointer action-icon"
          title="Output Formats"
          @click="${this.editPubOutputs.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this.pubDef?.lstOutputFormats.length > 0
            ? this.pubDef?.lstOutputFormats.map((x) => html ` <li><span>${x.name}</span></li> `)
            : html `
              <li>
                <span class="italic"> ${this.string["_no_item"]} </span>
              </li>
            `}
      </ul>
    `;
    }
    renderPubDef() {
        if (!this.pubDef)
            return html `
        <wex-row-item justifyContent="center">
          <span>${this.string["_publish_select_pubdef_inst"]}</span>
        </wex-row-item>
      `;
        const pubDef = html `
      ${this.renderCloseButton()} ${this.renderProjectName()}
      ${this.renderContent()} ${this.renderLanguages()} ${this.renderOutputs()}
    `;
        return pubDef;
    }
    renderDefinitionView() {
        return html `
      <section class="dev-view" ?loading="${this.isLoading}">
        ${this.renderLoading()} ${this.renderPubDef()}
      </section>
    `;
    }
    render() {
        return html `
      <wex-col-item
        justifyContent="flex-start"
        alignItems="center"
        height="500px"
      >
        ${this.renderDefinitionView()}
      </wex-col-item>
    `;
    }
};
__decorate([
    property({ type: Number })
], WexDefinitionViewPane.prototype, "selectedProjectId", void 0);
__decorate([
    property({ type: Number })
], WexDefinitionViewPane.prototype, "selectedPubDefId", void 0);
__decorate([
    state()
], WexDefinitionViewPane.prototype, "pubDef", void 0);
__decorate([
    state()
], WexDefinitionViewPane.prototype, "isLoading", void 0);
WexDefinitionViewPane = __decorate([
    customElement("wex-definition-view-pane")
], WexDefinitionViewPane);
export { WexDefinitionViewPane };
//# sourceMappingURL=definition-view-pane.js.map