{"version": 3, "file": "oxygen_sample_bookmap.js", "sourceRoot": "", "sources": ["../../../../src/test/ditamap-tree/files/oxygen_sample_bookmap.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,qBAAqB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;WA2B1B,CAAC", "sourcesContent": ["export const OXYGEN_SAMPLE_BOOKMAP = `<bookmap id=\"taskbook\">\r\n <booktitle>\r\n   <mainbooktitle>Product tasks</mainbooktitle>\r\n   <booktitlealt>Tasks and what they do</booktitlealt>\r\n </booktitle>\r\n <bookmeta>\r\n   <author><PERSON></author>\r\n   <bookrights>\r\n     <copyrfirst>\r\n       <year>2006</year>\r\n     </copyrfirst>\r\n     <bookowner>\r\n       <person href=\"janedoe.dita\"><PERSON></person>\r\n     </bookowner>\r\n   </bookrights>\r\n </bookmeta>\r\n <frontmatter>\r\n   <preface/>\r\n </frontmatter>\r\n   <chapter format=\"ditamap\" href=\"installing.ditamap\"/>\r\n   <chapter href=\"configuring.dita\"/>\r\n   <chapter href=\"maintaining.dita\">\r\n     <topicref href=\"maintainstorage.dita\"/>\r\n     <topicref href=\"maintainserver.dita\"/>\r\n     <topicref href=\"maintaindatabase.dita\"/>\r\n   </chapter>\r\n <appendix href=\"task_appendix.dita\"/>\r\n</bookmap>`;\r\n"]}