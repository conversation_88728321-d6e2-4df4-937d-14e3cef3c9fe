import "bds-tree";
import { LitElement, TemplateResult, html } from "lit";
import { DitamapTree } from "../lib/jsDitamap/ditamap-tree";
import { customElement, property, query } from "lit/decorators.js";
import { DitamapNode } from "lib/jsDitamap/ditamap-node";
import "./wex-ditamap-tree-toolbar.ts";
import "./wex-ditamap-icon.ts";
import { BDSTree } from "bds-tree";
import { classMap } from "lit/directives/class-map.js";
import {
  DitamapCommandManager,
  InsertCommand,
} from "lib/jsDitamap/ditamap-command-manager";

@customElement("wex-ditamap-tree")
export class WexDitamapTree extends LitElement {
  treeConfig: any;
  commandManager?: DitamapCommandManager;
  tree: DitamapTree | null = null;
  @property({ type: Object })
  root: DitamapNode | null = null;
  @property({ type: String })
  rootMap: string;
  @property({ type: Object })
  selectedNode: DitamapNode | null = null;
  @property({ type: String })
  status: "success" | "error" | "loading" = "loading";
  @query("bds-tree")
  treeEl?: BDSTree<DitamapNode>;

  constructor() {
    super();
    this.treeConfig = {
      labelPath: "title",
      idPath: "id",
      childPath: "children",
      showRoot: true,
      iconSet: "ditamap",
      hasUnselect: true,
      nodeRenderer: this.nodeRenderer.bind(this),
    };
    this.rootMap = "";
  }

  async connectedCallback() {
    super.connectedCallback();
    console.log("MAP: ", this.rootMap);
    this.tree = new DitamapTree(this.rootMap);
    this.status = "loading";
    await this.tree.load();
    this.root = this.tree.getRoot()!;
    this.status = "success";
  }

  updated() {
    if (!this.commandManager && this.tree && this.root) {
      this.commandManager = new DitamapCommandManager(this.tree, (root) => {
        this.root = root;
      });
    }
  }

  handleCut() {
    if (this.selectedNode) {
      console.log("Cut: ", this.selectedNode, this.treeEl);
      this.selectedNode.cut();
      this.treeEl?.requestUpdate();
    }
  }

  handleCopy() {
    if (this.selectedNode) {
      console.log("Copy: ", this.selectedNode);
    }
  }

  async handleInsert() {
    if (this.selectedNode) {
      await this.commandManager?.execute(
        new InsertCommand(this.selectedNode, TEMP_MAP)
      );
    }
  }

  handleTreeClick(e) {
    if (e.detail.isSelected) {
      this.selectedNode = e.detail.node;
    } else {
      this.selectedNode = null;
    }
  }

  /**
   * Controls the display of nodes in the tree
   */
  nodeRenderer(node: WexDitamapNode): TemplateResult {
    if (node.context?.cutId === node.key) {
      console.log("Do we have context?: ", node);
    }

    return html` <style>
        .is-cut {
          opacity: 0.25;
        }
      </style>
      <span
        class=${classMap({
          "is-cut": node.context?.cutId === node.key,
        })}
      >
        <wex-ditamap-icon icon=${node?.type}></wex-ditamap-icon>
        ${node.label}</span
      >`;
  }

  render() {
    return html` ${this.status == "loading"
      ? html`<div>Loading...</div>`
      : this.status == "error"
        ? html`<div>Error</div>`
        : html` <wex-ditamap-tree-toolbar
              .hasSelection="${!!this.selectedNode}"
              @insert="${this.handleInsert}"
              @cut="${this.handleCut}"
              @copy="${this.handleCopy}"
            ></wex-ditamap-tree-toolbar>
            <span
              >Selected: ${this.selectedNode?.title || "None"}
              ${this.selectedNode?.id}</span
            >
            <bds-tree
              @treeClick=${this.handleTreeClick}
              .config=${this.treeConfig}
              .root=${this.root}
            ></bds-tree>`}`;
  }
}

const TEMP_FILE = {
  ditaClass: "- topic/topic concept/concept ",
  imageHeight: -1,
  imageWidth: -1,
  isXml: true,
  kbResId: "Xdfv_1612",
  lblSeqId: 1612,
  lockorType: "NoLock",
  lineageId: 1612,
  mimeType: "text/xml",
  name: "begin.xml",
  resLblId: "/Content/begin_xi1609_1_1.xml",
};
// Content/submap_phones_xi1606_1_1.ditamap
const TEMP_MAP = {
  ditaClass: "- map/map ",
  imageHeight: -2147483648,
  imageWidth: -2147483648,
  isXml: true,
  kbResId: "Xdfv_1680",
  lblSeqId: 1680,
  lineageId: 1680,
  lockorType: "NoLock",
  mimeType: "text/xml",
  name: "dita20_quickstart_guide.ditamap",
  permissions: "RUDP",
  resLblId: "/Content/dita20_quickstart_guide_xi1680_1_1.ditamap",
  resPathId: "/Content/sample/dita/dita20/dita20_quickstart_guide.ditamap",
  rootElementName: "map",
  title: "DITA 2.0 Phone Quickstart Guide",
  verCreateDate: "2025-08-22T18:50:12Z",
  verCreator: "administrator",
  verNum: 1,
};

interface WexDitamapNode extends DitamapNode {
  key: string;
  label: string;
}
