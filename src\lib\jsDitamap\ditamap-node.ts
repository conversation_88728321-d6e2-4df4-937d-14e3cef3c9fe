import { DitamapUtils } from "./ditamap-utils";

export interface DitamapNode {
  /** Unique identifier for the node */
  // id: string;
  /** Display title for the node */
  title: string;
  /** Name of the containing map */
  containingMap: string | null;
  /** Path hierarchy to this node */
  mapPath: string[];
  /** Reference to the target resource */
  href: string;
  children: DitamapNode[];
  refElement: HTMLElement | null;
  structuralElement: HTMLElement;
}

export class RootMapNode implements DitamapNode {
  href: string;
  containingMap: string | null;
  refElement: null;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  title: string;
  children: DitamapNode[] = [];

  constructor(rootElement: HTMLElement, rootMapName: string) {
    this.href = rootMapName;
    this.containingMap = null;
    this.refElement = null;
    this.structuralElement = rootElement;
    this.title = DitamapUtils.getMapTitle(this.structuralElement);
  }
}

export class MapRefLikeElementNode implements DitamapNode {
  href: string;
  containingMap: string;
  refElement: HTMLElement;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapNode[] = [];
  title: string;

  constructor(
    refElement: HTMLElement,
    structuralElement: HTMLElement,
    containingMap: string
  ) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.structuralElement = structuralElement;
    this.title = DitamapUtils.getMapTitle(this.structuralElement);
  }
}

/**
 * Content elements reference content and provide structure.
 */
export class ContentNode implements DitamapNode {
  href: string;
  containingMap: string | null;
  refElement: HTMLElement;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapNode[] = [];
  title: string;

  constructor(refElement: HTMLElement, containingMap: string) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.structuralElement = refElement;
    this.title = DitamapUtils.getMapTitle(this.refElement);
  }

}
