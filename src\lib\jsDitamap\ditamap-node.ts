import { FileMeta } from "@bds/types";
import { DitamapUtils } from "./ditamap-utils";
import { createRefElement } from "./ditamap-templates";
import { DitamapContext } from "./ditamap-tree";

export interface DitamapNode {
  /** Unique identifier for the node */
  id: string;
  /** Display title for the node */
  title: string;
  /** Name of the containing map */
  containingMap: string | null;
  /** Path hierarchy to this node */
  mapPath: string[];
  /** Reference to the target resource */
  href: string | null;
  /** Child nodes */
  children: DitamapNode[];
  /** Type of the node */
  type: string;
  /** Context for the tree */
  context: DitamapContext;
  /** Reference to element inserted to map: mapref, part, topicref */
  refElement: HTMLElement | null;
  /** Reference to element resolved by the href: map, bookmap */
  structuralElement: HTMLElement | null;

  /**
   * Converts a file to a ref element that can be inserted into the map.
   */
  _fileToRefElement: (file: FileMeta) => HTMLElement;
  insertNodeFromFile: (file: FileMeta) => void;
  remove: () => void;
  // cut: () => void;
}

/**
 * Root node of the ditamap tree. E.g bookmap, map
 */
export class RootMapNode implements DitamapNode {
  href: string;
  containingMap: string | null;
  refElement: null;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  title: string;
  children: DitamapNode[] = [];
  id: string;
  context: DitamapContext;

  constructor({
    rootElement,
    rootMapName,
    context,
  }: {
    rootElement: HTMLElement;
    rootMapName: string;
    context?: DitamapContext;
  }) {
    this.href = rootMapName;
    this.containingMap = null;
    this.refElement = null;
    this.structuralElement = rootElement;
    this.title = DitamapUtils.getMapTitle(this.structuralElement);
    context ? (this.context = context) : null;
    this.id = this.structuralElement.getAttribute("data-id")!;
  }

  get type() {
    return this.structuralElement.tagName;
  }

  _insert(element: HTMLElement) {
    this.structuralElement.appendChild(element);
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (this.structuralElement.tagName == "bookmap") {
      if (file.rootElementName == "map") {
        return createRefElement("part", file);
      } else {
        return createRefElement("chapter", file);
      }
    }

    if (file.rootElementName == "map") {
      return createRefElement("mapref", file);
    } else {
      return createRefElement("topicref", file);
    }
  }

  insertNodeFromFile(file: FileMeta) {
    if (file.rootElementName === "bookmap") {
      throw Error("Cannot insert bookmap into bookmap");
    }

    if (file.rootElementName == "glossentry") {
      throw Error("Cannot insert glossentry into bookmap");
    }

    let refEl = this._fileToRefElement(file);
    this._insert(refEl);
    return;
  }
}

/**
 * References a ditamap - e.g part, mapref
 */
export class MapReferenceNode implements DitamapNode {
  href: string;
  containingMap: string;
  refElement: HTMLElement;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapNode[] = [];
  id: string;
  context?: DitamapContext;

  constructor({
    refElement,
    structuralElement,
    containingMap,
    context,
  }: {
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    containingMap: string;
    context?: DitamapContext;
  }) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.structuralElement = structuralElement;
    context ? (this.context = context) : null;
    this.id = this.structuralElement.getAttribute("data-id")!;
  }

  get title() {
    return (
      this.context?.metadata.get(this.href)?.title ??
      DitamapUtils.getMapTitle(this.structuralElement)
    );
  }

  get type() {
    return (
      this.context?.metadata.get(this.href)?.rootElementName ??
      this.structuralElement.tagName
    );
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createRefElement("mapref", file);
    }
    if (file.rootElementName == "glossentry") {
      return createRefElement("glossref", file);
    } else {
      return createRefElement("topicref", file);
    }
  }

  insertNodeFromFile(file: FileMeta) {
    if (file.rootElementName === "bookmap") {
      throw Error("Cannot insert bookmap into bookmap");
    }

    let refEl = this._fileToRefElement(file);
    this.structuralElement.appendChild(refEl);
  }
}

/**
 * Refecences topics - e.g topicref, chapter, appendix, glossref.
 */
export class TopicReferenceNode implements DitamapNode {
  href: string;
  containingMap: string;
  refElement: HTMLElement;
  structuralElement: null = null;
  mapPath: string[] = [];
  children: DitamapNode[] = [];
  context: DitamapContext;
  id: string;

  constructor({
    refElement,
    containingMap,
    context,
  }: {
    refElement: HTMLElement;
    containingMap: string;
    context: DitamapContext;
  }) {
    this.href = refElement.getAttribute("href")!;
    this.containingMap = containingMap;
    this.refElement = refElement;
    this.context = context;
    this.id = this.refElement.getAttribute("data-id")!;
  }

  get title() {
    return (
      this.context?.metadata.get(this.href)?.title ??
      DitamapUtils.getMapTitle(this.refElement)
    );
  }

  get type() {
    return (
      this.context?.metadata.get(this.href)?.rootElementName ??
      this.refElement.tagName
    );
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createRefElement("mapref", file);
    }
    if (file.rootElementName == "glossentry") {
      return createRefElement("glossref", file);
    } else {
      return createRefElement("topicref", file);
    }
  }

  insertNodeFromFile(file: FileMeta) {
    if (file.rootElementName === "bookmap") {
      throw Error("Cannot insert bookmap into bookmap");
    }

    let refEl = this._fileToRefElement(file);
    this.refElement.appendChild(refEl);
  }

  remove() {
    this.refElement.remove();
  }

  cut() {
    this.context.buffer = this;
    this.context.cutId = this.id;
    console.log(this.context);
  }
}

/**
 * Provides structure to the map. E.g topichead, topicgroup
 */
export class StructuralNode implements DitamapNode {
  containingMap: string;
  refElement: null = null;
  structuralElement: HTMLElement;
  mapPath: string[] = [];
  children: DitamapNode[] = [];
  id: string;

  constructor(structuralElement: HTMLElement, containingMap: string) {
    this.containingMap = containingMap;
    this.structuralElement = structuralElement;
    this.id = this.structuralElement.getAttribute("data-id")!;
  }

  get title() {
    return (
      this.structuralElement.getAttribute("navtitle") ??
      this.structuralElement.tagName
    );
  }

  get type() {
    return this.structuralElement.tagName;
  }

  get href() {
    return this.containingMap + "#" + this.title;
  }

  _fileToRefElement(file: FileMeta): HTMLElement {
    if (file.rootElementName == "map") {
      return createRefElement("mapref", file);
    }
    if (file.rootElementName == "glossentry") {
      return createRefElement("glossref", file);
    } else {
      return createRefElement("topicref", file);
    }
  }

  insertNodeFromFile(file: FileMeta) {
    if (file.rootElementName === "bookmap") {
      throw Error("Cannot insert bookmap into bookmap");
    }
    let refEl = this._fileToRefElement(file);
    this.structuralElement.appendChild(refEl);
  }
}
