{"version": 3, "file": "publish-defs.js", "sourceRoot": "", "sources": ["../../../src/pages/publish/publish-defs.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACzD,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAExC,OAAO,6BAA6B,CAAC;AAErC,OAAO,gCAAgC,CAAC;AACxC,OAAO,kCAAkC,CAAC;AAC1C,OAAO,mDAAmD,CAAC;AAC3D,OAAO,+CAA+C,CAAC;AACvD,OAAO,iCAAiC,CAAC;AAEzC,+BAA+B;AAC/B,kCAAkC;AAG3B,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;IAA3C;;QACI,sBAAiB,GAAkB,IAAI,CAAC;QACxC,qBAAgB,GAAkB,IAAI,CAAC;QAExC,UAAK,GAAQ,aAAa,CAAC,KAAK,CAAC;QACjC,WAAM,GAAU,EAAE,CAAC;IA0I7B,CAAC;IAvIC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,iBAAiB,EAAE,CAAC;YACpD,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,aAAa,CAAC,QAAQ,CAAC,sBAAsB,EAAE;oBAC7C,UAAU;iBACX,CAAC,CAAC;YACL,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,2BAA2B,EAAE,CAAC;YAC5D,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACpB,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE;oBAC3C,QAAQ;iBACT,CAAC,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;YAC7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE;oBACnC,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,2BAA2B,CAAC,CAAc;QACxC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED,0BAA0B;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,kBAAkB;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAA;;;iCAGQ,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;kCACzC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC;;KAExE,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,kBAAkB;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAA;;;6BAGI,IAAI,CAAC,iBAAiB;4BACvB,IAAI,CAAC,gBAAgB;;KAE5C,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,eAAe;QACb,iBAAiB;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAA;;;YAGb,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE;;;KAG7D,CAAC;QACF,kBAAkB;QAClB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;;UAGL,IAAI,CAAC,eAAe,EAAE;;KAE3B,CAAC;IACJ,CAAC;CACF,CAAA;AA9IU;IAAR,KAAK,EAAE;6DAAyC;AACxC;IAAR,KAAK,EAAE;4DAAwC;AAFrC,kBAAkB;IAD9B,aAAa,CAAC,uBAAuB,CAAC;GAC1B,kBAAkB,CA+I9B", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"@vaadin/vaadin-split-layout\";\r\n\r\nimport \"../../components/base/row-item\";\r\nimport \"../../components/common/mainpane\";\r\nimport \"../../components/publish/publish-definitions-pane\";\r\nimport \"../../components/publish/definition-view-pane\";\r\nimport \"../../components/header/publish\";\r\n\r\n// TODO -- add to initial state\r\n// const DEBOUNCE_DURATION = 1000;\r\n\r\n@customElement(\"wex-page-publish-defs\")\r\nexport class WexPagePublishDefs extends LitElement {\r\n  @state() selectedProjectId: number | null = null;\r\n  @state() selectedPubDefId: number | null = null;\r\n\r\n  private state: any = storeInstance.state;\r\n  private string: any[] = [];\r\n  private subscription: any;\r\n\r\n  static get styles() {\r\n    return css`\r\n      #publish-defs-container {\r\n        flex-grow: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        max-height: 100%;\r\n      }\r\n      .mainpane {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: stretch;\r\n        justify-content: space-evenly;\r\n        justify-content: center;\r\n        color: var(--font-color);\r\n        height: 100%;\r\n        max-height: 100%;\r\n      }\r\n      .mainpane > *:not(:last-child) {\r\n        margin-bottom: 1rem;\r\n        border: 1px solid #f00;\r\n      }\r\n      .split-layout {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n      .right,\r\n      .left {\r\n        padding: 1rem;\r\n      }\r\n    `;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this._init();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n  }\r\n\r\n  async _init() {\r\n    try {\r\n      const categories = await wexlib.getProjCategories();\r\n      if (categories.length) {\r\n        storeInstance.dispatch(\"setPublishCategories\", {\r\n          categories,\r\n        });\r\n      }\r\n\r\n      const projects = await wexlib.getProjectsByProjCategoryId();\r\n      if (projects.length) {\r\n        storeInstance.dispatch(\"setPublishProjects\", {\r\n          projects,\r\n        });\r\n      }\r\n\r\n      const pubDefs = await wexlib.getAllPubDefs();\r\n      if (pubDefs.length) {\r\n        storeInstance.dispatch(\"setPubDefs\", {\r\n          pubDefs,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error initializing publish definitions:\", error);\r\n    }\r\n  }\r\n\r\n  _handleSinglePubDefSelected(e: CustomEvent) {\r\n    this.selectedProjectId = e.detail.projectId;\r\n    this.selectedPubDefId = e.detail.pubDefId;\r\n  }\r\n\r\n  _handleResetSelectedPubDef() {\r\n    this.selectedProjectId = null;\r\n    this.selectedPubDefId = null;\r\n  }\r\n\r\n  _renderPubDefsPane() {\r\n    const template = html`\r\n      <wex-publish-definitions-pane\r\n        class=\"left\"\r\n        @reset-selected-pubdef=${this._handleResetSelectedPubDef.bind(this)}\r\n        @single-pubdef-selected=${this._handleSinglePubDefSelected.bind(this)}\r\n      ></wex-publish-definitions-pane>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  _renderDefViewPane() {\r\n    const template = html`\r\n      <wex-definition-view-pane\r\n        class=\"right\"\r\n        .selectedProjectId=${this.selectedProjectId}\r\n        .selectedPubDefId=${this.selectedPubDefId}\r\n      ></wex-definition-view-pane>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  _renderMainPane() {\r\n    // <wex-row-item>\r\n    const template = html`\r\n      <section class=\"mainpane\">\r\n        <vaadin-split-layout class=\"split-layout\">\r\n          ${this._renderPubDefsPane()} ${this._renderDefViewPane()}\r\n        </vaadin-split-layout>\r\n      </section>\r\n    `;\r\n    // </wex-row-item>\r\n    return template;\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <div id=\"publish-defs-container\">\r\n        <wex-publish-header></wex-publish-header>\r\n        ${this._renderMainPane()}\r\n      </div>\r\n    `;\r\n  }\r\n}\r\n"]}