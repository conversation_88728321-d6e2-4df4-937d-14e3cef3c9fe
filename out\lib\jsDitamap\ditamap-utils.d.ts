export declare class DitamapUtils {
    /**
     * Checks if an html element is a mapref or part
     * @param element
     */
    static isMapRefLike(element: HTMLElement): boolean;
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isTopicRefLike(element: HTMLElement): boolean;
    /**
     * Gets the href used to identify the map, if no href assume it is the root map
     * @param element - xml element
     * @param rootMapName - name of the root map
     */
    static getMapName(element: HTMLElement, rootMapName?: string): string | undefined;
    static getMapTitle(element: HTMLElement): string;
    static getDirtyTitle(element: HTMLElement): string;
    static removeXmlNoise(str: string): string;
}
//# sourceMappingURL=ditamap-utils.d.ts.map