import { FileMeta } from "@bds/types";
import { TopicReferenceNode, MapReferenceNode } from "./ditamap-node";
export declare class DitamapUtils {
    /**
     * Checks if an html element is a mapref or part
     * @param element
     */
    static isMapReference(element: HTMLElement): boolean;
    /** Determines which type of node should be used for an xml element */
    static getDitamapNodeClass(element: HTMLElement): string | undefined;
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isTopicReference(element: HTMLElement): boolean;
    /**
   * Gets the map from the workspace, if not there then fetch
   * @param href
   * @param workspace
   */
    static getMap(href: string, workspace: Map<string, HTMLElement>): HTMLElement | undefined;
    /**
     * Gets the href used to identify the map, if no href assume it is the root map
     * @param element - xml element
     * @param rootMapName - name of the root map
     */
    static getMapName(element: HTMLElement, rootMapName?: string): string | undefined;
    static getMapTitle(element: HTMLElement): string;
    static getDirtyTitle(element: HTMLElement): string;
    static removeXmlNoise(str: string): string;
    static fileToNode(parentTagName: string, file: FileMeta): MapReferenceNode | TopicReferenceNode | undefined;
}
