/**
 * The ditamap editor involves many parts
 *  1. Storing a hashmap of the files
 *  2. Building a JSON tree from the xml
 *  3. Displaying the tree
 *  4. Editing the tree
 *  5. Saving the tree
 *  6. Validating the tree
 */
import { DitamapNode } from "../../lib/jsDitamap/ditamap-node";
import { DitamapTree } from "../../lib/jsDitamap/ditamap-tree";
import { PHONES_TREE } from "./data/phones-tree";
import { PHONES_1_BOOKMAP, SUBMAP_PHONES } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";

describe("Building JSON tree from xml", () => {
  it("builds phones tree", () => {
    let bookmap = new DOMParser().parseFromString(
      PHONES_1_BOOKMAP,
      "application/xml"
    ).documentElement;
    let submap = new DOMParser().parseFromString(
      SUBMAP_PHONES,
      "application/xml"
    ).documentElement;

    let workspace = new Map<string, HTMLElement>();
    workspace.set("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", bookmap);
    workspace.set("/Content/submap_phones_xi1609_1_1.ditamap", submap);

    let tree = new DitamapTree(
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      workspace
    );

    expect(pruneTree(PHONES_TREE, ["href", "children"])).to.deep.equal(
      pruneTree(tree.root, [
        "href",
        "children",
        // "containingMap",
      ])
    );
  });
});

describe("Insert reference", () => {
  describe("Into well formed bookmap", () => {
    it.skip("inserts a map as a part", () => {

    })
  })
})

const pruneTree = <T extends DitamapNode>(
  current: DitamapNode | null,
  fields: (keyof T)[]
) => {
  if (!current) return null;

  let pruned: T = {} as T;

  fields.forEach((field) => {
    if (current[field] != undefined) pruned[field] = current[field];
  });

  pruned.children = current.children.map((child) => pruneTree(child, fields));
  return pruned;
};
