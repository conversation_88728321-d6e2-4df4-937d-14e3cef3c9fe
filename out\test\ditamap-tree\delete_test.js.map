{"version": 3, "file": "delete_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/delete_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,IAAI,YAAY,GAAG;IACjB,SAAS,EAAE,IAAI,GAAG,EAAE;IACpB,QAAQ,EAAE,IAAI,GAAG,EAAE;IACnB,KAAK,EAAE,IAAI,GAAG,EAAU;IACxB,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;CACb,CAAC;AAEF,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;IACvB,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACpC,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACzC,IAAI,IAAI,GAAG,IAAI,kBAAkB,CAAC;YAChC,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,4CAA4C;YAC3D,OAAO,EAAE,YAAY;SACtB,CAAC,CAAC;QAEH,iBAAiB;QACjB,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;QAC3C,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACtC,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC", "sourcesContent": ["import { TopicReferenceNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { createXmlElement } from \"./data/elements\";\r\nimport { SUBMAP_PHONES } from \"./files/phones_1_bookmap\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\nlet emptyContext = {\r\n  workspace: new Map(),\r\n  metadata: new Map(),\r\n  dirty: new Set<string>(),\r\n  cutId: null,\r\n  buffer: null,\r\n};\r\n\r\ndescribe(\"Rootmap\", () => {\r\n  it.skip(\"Blocks rootmap deletion\", () => {});\r\n});\r\n\r\ndescribe(\"Map Reference\", () => {\r\n  it.skip(\"Deletes mapref\", () => {});\r\n  it.skip(\"Deletes part\", () => {});\r\n});\r\n\r\ndescribe(\"Topic Reference\", () => {\r\n  it(\"Deletes topicref\", () => {\r\n    let el = createXmlElement(SUBMAP_PHONES);\r\n    let node = new TopicReferenceNode({\r\n      refElement: el,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      context: emptyContext,\r\n    });\r\n\r\n    // node.remove();\r\n    expect(node?.structuralElement).to.be.null;\r\n    expect(node?.refElement).to.be.null;\r\n  });\r\n  it.skip(\"Deletes chapter\", () => {});\r\n});\r\n\r\ndescribe(\"Structural Element\", () => {\r\n  it.skip(\"Deletes topichead\", () => {});\r\n});\r\n"]}