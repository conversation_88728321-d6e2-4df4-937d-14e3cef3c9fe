{"version": 3, "file": "ditamap-node.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-node.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAkB/C,MAAM,OAAO,WAAW;IAStB,YAAY,WAAwB,EAAE,WAAmB;QAJzD,YAAO,GAAa,EAAE,CAAC;QAEvB,aAAQ,GAAkB,EAAE,CAAC;QAG3B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;CACF;AAED,MAAM,OAAO,qBAAqB;IAShC,YACE,UAAuB,EACvB,iBAA8B,EAC9B,aAAqB;QAPvB,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAQ3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IAStB,YAAY,UAAuB,EAAE,aAAqB;QAJ1D,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAI3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;CAEF", "sourcesContent": ["import { DitamapUtils } from \"./ditamap-utils\";\r\n\r\nexport interface DitamapNode {\r\n  /** Unique identifier for the node */\r\n  // id: string;\r\n  /** Display title for the node */\r\n  title: string;\r\n  /** Name of the containing map */\r\n  containingMap: string | null;\r\n  /** Path hierarchy to this node */\r\n  mapPath: string[];\r\n  /** Reference to the target resource */\r\n  href: string;\r\n  children: DitamapNode[];\r\n  refElement: HTMLElement | null;\r\n  structuralElement: HTMLElement;\r\n}\r\n\r\nexport class RootMapNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string | null;\r\n  refElement: null;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  title: string;\r\n  children: DitamapNode[] = [];\r\n\r\n  constructor(rootElement: HTMLElement, rootMapName: string) {\r\n    this.href = rootMapName;\r\n    this.containingMap = null;\r\n    this.refElement = null;\r\n    this.structuralElement = rootElement;\r\n    this.title = DitamapUtils.getMapTitle(this.structuralElement);\r\n  }\r\n}\r\n\r\nexport class MapRefLikeElementNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string;\r\n  refElement: HTMLElement;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n  title: string;\r\n\r\n  constructor(\r\n    refElement: HTMLElement,\r\n    structuralElement: HTMLElement,\r\n    containingMap: string\r\n  ) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.structuralElement = structuralElement;\r\n    this.title = DitamapUtils.getMapTitle(this.structuralElement);\r\n  }\r\n}\r\n\r\n/**\r\n * Content elements reference content and provide structure.\r\n */\r\nexport class ContentNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string | null;\r\n  refElement: HTMLElement;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n  title: string;\r\n\r\n  constructor(refElement: HTMLElement, containingMap: string) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.structuralElement = refElement;\r\n    this.title = DitamapUtils.getMapTitle(this.refElement);\r\n  }\r\n\r\n}\r\n"]}