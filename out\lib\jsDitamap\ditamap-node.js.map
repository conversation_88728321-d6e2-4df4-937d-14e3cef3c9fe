{"version": 3, "file": "ditamap-node.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-node.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAkCvD;;GAEG;AACH,MAAM,OAAO,WAAW;IAWtB,YAAY,EACV,WAAW,EACX,WAAW,EACX,OAAO,GAKR;QAdD,YAAO,GAAa,EAAE,CAAC;QAEvB,aAAQ,GAAkB,EAAE,CAAC;QAa3B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9D,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAE,CAAC;IAC5D,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;IACxC,CAAC;IAED,OAAO,CAAC,OAAoB;QAC1B,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAAC,IAAc;QAC9B,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,EAAE,CAAC;gBAClC,OAAO,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,OAAO,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,IAAc;QAC/B,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,EAAE,CAAC;YACzC,MAAM,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACpB,OAAO;IACT,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAU3B,YAAY,EACV,UAAU,EACV,iBAAiB,EACjB,aAAa,EACb,OAAO,GAMR;QAfD,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAe3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAE,CAAC;IAC5D,CAAC;IAED,IAAI,KAAK;QACP,OAAO,CACL,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;YAC5C,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CACjD,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CACL,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,eAAe;YACtD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC/B,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,IAAc;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,EAAE,CAAC;YACzC,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,IAAc;QAC/B,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAU7B,YAAY,EACV,UAAU,EACV,aAAa,EACb,OAAO,GAKR;QAdD,sBAAiB,GAAS,IAAI,CAAC;QAC/B,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAa3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAE,CAAC;IACrD,CAAC;IAED,IAAI,KAAK;QACP,OAAO,CACL,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK;YAC5C,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAC1C,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CACL,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,eAAe;YACtD,IAAI,CAAC,UAAU,CAAC,OAAO,CACxB,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,IAAc;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,EAAE,CAAC;YACzC,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,IAAc;QAC/B,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;IAED,GAAG;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,cAAc;IAQzB,YAAY,iBAA8B,EAAE,aAAqB;QANjE,eAAU,GAAS,IAAI,CAAC;QAExB,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAI3B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAE,CAAC;IAC5D,CAAC;IAED,IAAI,KAAK;QACP,OAAO,CACL,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC/B,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;IACxC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;IAC/C,CAAC;IAED,iBAAiB,CAAC,IAAc;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,KAAK,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,YAAY,EAAE,CAAC;YACzC,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,OAAO,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,IAAc;QAC/B,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;CACF", "sourcesContent": ["import { FileMeta } from \"@bds/types\";\r\nimport { DitamapUtils } from \"./ditamap-utils\";\r\nimport { createRefElement } from \"./ditamap-templates\";\r\nimport { DitamapContext } from \"./ditamap-tree\";\r\n\r\nexport interface DitamapNode {\r\n  /** Unique identifier for the node */\r\n  id: string;\r\n  /** Display title for the node */\r\n  title: string;\r\n  /** Name of the containing map */\r\n  containingMap: string | null;\r\n  /** Path hierarchy to this node */\r\n  mapPath: string[];\r\n  /** Reference to the target resource */\r\n  href: string | null;\r\n  /** Child nodes */\r\n  children: DitamapNode[];\r\n  /** Type of the node */\r\n  type: string;\r\n  /** Context for the tree */\r\n  context: DitamapContext;\r\n  /** Reference to element inserted to map: mapref, part, topicref */\r\n  refElement: HTMLElement | null;\r\n  /** Reference to element resolved by the href: map, bookmap */\r\n  structuralElement: HTMLElement | null;\r\n\r\n  /**\r\n   * Converts a file to a ref element that can be inserted into the map.\r\n   */\r\n  _fileToRefElement: (file: FileMeta) => HTMLElement;\r\n  insertNodeFromFile: (file: FileMeta) => void;\r\n  remove: () => void;\r\n  // cut: () => void;\r\n}\r\n\r\n/**\r\n * Root node of the ditamap tree. E.g bookmap, map\r\n */\r\nexport class RootMapNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string | null;\r\n  refElement: null;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  title: string;\r\n  children: DitamapNode[] = [];\r\n  id: string;\r\n  context: DitamapContext;\r\n\r\n  constructor({\r\n    rootElement,\r\n    rootMapName,\r\n    context,\r\n  }: {\r\n    rootElement: HTMLElement;\r\n    rootMapName: string;\r\n    context?: DitamapContext;\r\n  }) {\r\n    this.href = rootMapName;\r\n    this.containingMap = null;\r\n    this.refElement = null;\r\n    this.structuralElement = rootElement;\r\n    this.title = DitamapUtils.getMapTitle(this.structuralElement);\r\n    context ? (this.context = context) : null;\r\n    this.id = this.structuralElement.getAttribute(\"data-id\")!;\r\n  }\r\n\r\n  get type() {\r\n    return this.structuralElement.tagName;\r\n  }\r\n\r\n  _insert(element: HTMLElement) {\r\n    this.structuralElement.appendChild(element);\r\n  }\r\n\r\n  _fileToRefElement(file: FileMeta): HTMLElement {\r\n    if (this.structuralElement.tagName == \"bookmap\") {\r\n      if (file.rootElementName == \"map\") {\r\n        return createRefElement(\"part\", file);\r\n      } else {\r\n        return createRefElement(\"chapter\", file);\r\n      }\r\n    }\r\n\r\n    if (file.rootElementName == \"map\") {\r\n      return createRefElement(\"mapref\", file);\r\n    } else {\r\n      return createRefElement(\"topicref\", file);\r\n    }\r\n  }\r\n\r\n  insertNodeFromFile(file: FileMeta) {\r\n    if (file.rootElementName === \"bookmap\") {\r\n      throw Error(\"Cannot insert bookmap into bookmap\");\r\n    }\r\n\r\n    if (file.rootElementName == \"glossentry\") {\r\n      throw Error(\"Cannot insert glossentry into bookmap\");\r\n    }\r\n\r\n    let refEl = this._fileToRefElement(file);\r\n    this._insert(refEl);\r\n    return;\r\n  }\r\n}\r\n\r\n/**\r\n * References a ditamap - e.g part, mapref\r\n */\r\nexport class MapReferenceNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string;\r\n  refElement: HTMLElement;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n  id: string;\r\n  context?: DitamapContext;\r\n\r\n  constructor({\r\n    refElement,\r\n    structuralElement,\r\n    containingMap,\r\n    context,\r\n  }: {\r\n    refElement: HTMLElement;\r\n    structuralElement: HTMLElement;\r\n    containingMap: string;\r\n    context?: DitamapContext;\r\n  }) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.structuralElement = structuralElement;\r\n    context ? (this.context = context) : null;\r\n    this.id = this.structuralElement.getAttribute(\"data-id\")!;\r\n  }\r\n\r\n  get title() {\r\n    return (\r\n      this.context?.metadata.get(this.href)?.title ??\r\n      DitamapUtils.getMapTitle(this.structuralElement)\r\n    );\r\n  }\r\n\r\n  get type() {\r\n    return (\r\n      this.context?.metadata.get(this.href)?.rootElementName ??\r\n      this.structuralElement.tagName\r\n    );\r\n  }\r\n\r\n  _fileToRefElement(file: FileMeta): HTMLElement {\r\n    if (file.rootElementName == \"map\") {\r\n      return createRefElement(\"mapref\", file);\r\n    }\r\n    if (file.rootElementName == \"glossentry\") {\r\n      return createRefElement(\"glossref\", file);\r\n    } else {\r\n      return createRefElement(\"topicref\", file);\r\n    }\r\n  }\r\n\r\n  insertNodeFromFile(file: FileMeta) {\r\n    if (file.rootElementName === \"bookmap\") {\r\n      throw Error(\"Cannot insert bookmap into bookmap\");\r\n    }\r\n\r\n    let refEl = this._fileToRefElement(file);\r\n    this.structuralElement.appendChild(refEl);\r\n  }\r\n}\r\n\r\n/**\r\n * Refecences topics - e.g topicref, chapter, appendix, glossref.\r\n */\r\nexport class TopicReferenceNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string;\r\n  refElement: HTMLElement;\r\n  structuralElement: null = null;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n  context: DitamapContext;\r\n  id: string;\r\n\r\n  constructor({\r\n    refElement,\r\n    containingMap,\r\n    context,\r\n  }: {\r\n    refElement: HTMLElement;\r\n    containingMap: string;\r\n    context: DitamapContext;\r\n  }) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.context = context;\r\n    this.id = this.refElement.getAttribute(\"data-id\")!;\r\n  }\r\n\r\n  get title() {\r\n    return (\r\n      this.context?.metadata.get(this.href)?.title ??\r\n      DitamapUtils.getMapTitle(this.refElement)\r\n    );\r\n  }\r\n\r\n  get type() {\r\n    return (\r\n      this.context?.metadata.get(this.href)?.rootElementName ??\r\n      this.refElement.tagName\r\n    );\r\n  }\r\n\r\n  _fileToRefElement(file: FileMeta): HTMLElement {\r\n    if (file.rootElementName == \"map\") {\r\n      return createRefElement(\"mapref\", file);\r\n    }\r\n    if (file.rootElementName == \"glossentry\") {\r\n      return createRefElement(\"glossref\", file);\r\n    } else {\r\n      return createRefElement(\"topicref\", file);\r\n    }\r\n  }\r\n\r\n  insertNodeFromFile(file: FileMeta) {\r\n    if (file.rootElementName === \"bookmap\") {\r\n      throw Error(\"Cannot insert bookmap into bookmap\");\r\n    }\r\n\r\n    let refEl = this._fileToRefElement(file);\r\n    this.refElement.appendChild(refEl);\r\n  }\r\n\r\n  remove() {\r\n    this.refElement.remove();\r\n  }\r\n\r\n  cut() {\r\n    this.context.buffer = this;\r\n    this.context.cutId = this.id;\r\n    console.log(this.context);\r\n  }\r\n}\r\n\r\n/**\r\n * Provides structure to the map. E.g topichead, topicgroup\r\n */\r\nexport class StructuralNode implements DitamapNode {\r\n  containingMap: string;\r\n  refElement: null = null;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n  id: string;\r\n\r\n  constructor(structuralElement: HTMLElement, containingMap: string) {\r\n    this.containingMap = containingMap;\r\n    this.structuralElement = structuralElement;\r\n    this.id = this.structuralElement.getAttribute(\"data-id\")!;\r\n  }\r\n\r\n  get title() {\r\n    return (\r\n      this.structuralElement.getAttribute(\"navtitle\") ??\r\n      this.structuralElement.tagName\r\n    );\r\n  }\r\n\r\n  get type() {\r\n    return this.structuralElement.tagName;\r\n  }\r\n\r\n  get href() {\r\n    return this.containingMap + \"#\" + this.title;\r\n  }\r\n\r\n  _fileToRefElement(file: FileMeta): HTMLElement {\r\n    if (file.rootElementName == \"map\") {\r\n      return createRefElement(\"mapref\", file);\r\n    }\r\n    if (file.rootElementName == \"glossentry\") {\r\n      return createRefElement(\"glossref\", file);\r\n    } else {\r\n      return createRefElement(\"topicref\", file);\r\n    }\r\n  }\r\n\r\n  insertNodeFromFile(file: FileMeta) {\r\n    if (file.rootElementName === \"bookmap\") {\r\n      throw Error(\"Cannot insert bookmap into bookmap\");\r\n    }\r\n    let refEl = this._fileToRefElement(file);\r\n    this.structuralElement.appendChild(refEl);\r\n  }\r\n}\r\n"]}