{"version": 3, "file": "ditamap-node.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-node.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAmB7C,MAAM,OAAO,WAAW;IAQtB,YAAY,WAAwB,EAAE,WAAmB;QAHzD,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAG3B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;IACxC,CAAC;CACF;AAED,MAAM,OAAO,qBAAqB;IAQhC,YACE,UAAuB,EACvB,iBAA8B,EAC9B,aAAqB;QANvB,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAO3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAED,IAAI,KAAK;QACP,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IAQtB,YAAY,UAAuB,EAAE,aAAqB;QAH1D,YAAO,GAAa,EAAE,CAAC;QACvB,aAAQ,GAAkB,EAAE,CAAC;QAG3B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;IACtC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["import { DitamapTree } from \"./ditamap-tree\";\r\n\r\nexport interface DitamapNode {\r\n  /** Unique identifier for the node */\r\n  // id: string;\r\n  /** Display title for the node */\r\n  title: string;\r\n  /** Name of the containing map */\r\n  containingMap: string | null;\r\n  /** Path hierarchy to this node */\r\n  mapPath: string[];\r\n  /** Reference to the target resource */\r\n  href: string;\r\n  tagName: string;\r\n  children: DitamapNode[];\r\n  refElement: HTMLElement | null;\r\n  structuralElement: HTMLElement;\r\n}\r\n\r\nexport class RootMapNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string | null;\r\n  refElement: null;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n\r\n  constructor(rootElement: HTMLElement, rootMapName: string) {\r\n    this.href = rootMapName;\r\n    this.containingMap = null;\r\n    this.refElement = null;\r\n    this.structuralElement = rootElement;\r\n  }\r\n\r\n  get title() {\r\n    return DitamapTree.getMapTitle(this.structuralElement);\r\n  }\r\n\r\n  get tagName() {\r\n    return this.structuralElement.tagName;\r\n  }\r\n}\r\n\r\nexport class MapRefLikeElementNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string;\r\n  refElement: HTMLElement;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n\r\n  constructor(\r\n    refElement: HTMLElement,\r\n    structuralElement: HTMLElement,\r\n    containingMap: string\r\n  ) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.structuralElement = structuralElement;\r\n  }\r\n\r\n  get title() {\r\n    return DitamapTree.getMapTitle(this.refElement);\r\n  }\r\n\r\n  get tagName() {\r\n    return this.refElement.tagName;\r\n  }\r\n}\r\n\r\n/**\r\n * Content elements reference content and provide structure.\r\n */\r\nexport class ContentNode implements DitamapNode {\r\n  href: string;\r\n  containingMap: string | null;\r\n  refElement: HTMLElement;\r\n  structuralElement: HTMLElement;\r\n  mapPath: string[] = [];\r\n  children: DitamapNode[] = [];\r\n\r\n  constructor(refElement: HTMLElement, containingMap: string) {\r\n    this.href = refElement.getAttribute(\"href\")!;\r\n    this.containingMap = containingMap;\r\n    this.refElement = refElement;\r\n    this.structuralElement = refElement;\r\n  }\r\n\r\n  get title() {\r\n    return DitamapTree.getMapTitle(this.refElement);\r\n  }\r\n\r\n  get tagName() {\r\n    return this.refElement.tagName;\r\n  }\r\n}\r\n"]}