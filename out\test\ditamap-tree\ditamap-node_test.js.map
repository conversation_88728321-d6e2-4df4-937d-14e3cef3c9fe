{"version": 3, "file": "ditamap-node_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap-node_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AACtF,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C;;;;;;;;aAQO,EACP,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,IAAI,GAAG,IAAI,WAAW,CACxB,OAAO,EACP,4CAA4C,CAC7C,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,EAAE,4CAA4C;YAClD,aAAa,EAAE,IAAI;YACnB,KAAK,EAAE,oBAAoB;YAC3B,UAAU,EAAE,IAAI;YAChB,iBAAiB,EAAE,OAAO;YAC1B,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C;;;OAGC,EACD,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,iBAAiB,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CACrD;;;SAGG,EACH,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,IAAI,GAAG,IAAI,qBAAqB,CAClC,OAAO,EACP,iBAAiB,EACjB,4CAA4C,CAC7C,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,EAAE,2CAA2C;YACjD,aAAa,EAAE,4CAA4C;YAC3D,KAAK,EAAE,eAAe;YACtB,UAAU,EAAE,OAAO;YACnB,iBAAiB,EAAE,iBAAiB;YACpC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { MapRefLikeElementNode, RootMapNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Root Map Node\", () => {\r\n  it(\"should create node from bookmap\", () => {\r\n    let element = new DOMParser().parseFromString(\r\n      `\r\n          <bookmap \r\n          class=\"- map/map bookmap/bookmap \">\r\n              <booktitle class=\"- topic/title bookmap/booktitle \">\r\n          <mainbooktitle class=\"- topic/ph bookmap/mainbooktitle \">\r\n              <?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>\r\n      </booktitle>\r\n          </bookmap>\r\n            `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let node = new RootMapNode(\r\n      element,\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n    );\r\n    expect(node).to.deep.equal({\r\n      href: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      containingMap: null,\r\n      title: \"Phone 1 User Guide\",\r\n      refElement: null,\r\n      structuralElement: element,\r\n      mapPath: [],\r\n      children: [],\r\n    });\r\n  });\r\n});\r\ndescribe(\"Map Ref Like Node\", () => {\r\n  it(\"should create node from part\", () => {\r\n    let element = new DOMParser().parseFromString(\r\n      `\r\n        <part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n          class=\"- map/topicref bookmap/part \" />\r\n      `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let structuralElement = new DOMParser().parseFromString(\r\n      `\r\n          <map title=\"Submap Phones\"\r\n      class=\"- map/map \"></map>\r\n        `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let node = new MapRefLikeElementNode(\r\n      element,\r\n      structuralElement,\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n    );\r\n    expect(node).to.deep.equal({\r\n      href: \"/Content/submap_phones_xi1609_1_1.ditamap\",\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      title: \"Submap Phones\",\r\n      refElement: element,\r\n      structuralElement: structuralElement,\r\n      mapPath: [],\r\n      children: [],\r\n    });\r\n  });\r\n});\r\n"]}