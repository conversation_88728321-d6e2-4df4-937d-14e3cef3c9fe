{"version": 3, "file": "ditamap-node_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap-node_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C;;;;;;;;aAQO,EACP,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC;YACzB,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,EAAE,4CAA4C;YAClD,aAAa,EAAE,IAAI;YACnB,KAAK,EAAE,oBAAoB;YAC3B,UAAU,EAAE,IAAI;YAChB,iBAAiB,EAAE,OAAO;YAC1B,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { RootMapNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Root Map Node\", () => {\r\n  it(\"should create node from bookmap\", () => {\r\n    let element = new DOMParser().parseFromString(\r\n      `\r\n          <bookmap \r\n          class=\"- map/map bookmap/bookmap \">\r\n              <booktitle class=\"- topic/title bookmap/booktitle \">\r\n          <mainbooktitle class=\"- topic/ph bookmap/mainbooktitle \">\r\n              <?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>\r\n      </booktitle>\r\n          </bookmap>\r\n            `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let node = new RootMapNode({\r\n      rootElement: element,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    expect(node).to.deep.equal({\r\n      href: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      containingMap: null,\r\n      title: \"Phone 1 User Guide\",\r\n      refElement: null,\r\n      structuralElement: element,\r\n      mapPath: [],\r\n      children: [],\r\n    });\r\n  });\r\n});\r\n"]}