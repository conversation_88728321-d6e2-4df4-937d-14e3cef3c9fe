var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, css, html } from "lit";
import { customElement, property } from "lit/decorators.js";
import "@ui5/webcomponents/dist/Button.js";
let WexDitamapTreeToolbar = class WexDitamapTreeToolbar extends LitElement {
    constructor() {
        super(...arguments);
        this.hasSelection = false;
    }
    _emit(event) {
        this.dispatchEvent(new CustomEvent(event, { bubbles: true, composed: true }));
    }
    render() {
        return html `
      <ul class="toolbar">
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("insert")}
          >
            <iron-icon icon="vaadin:file-add"></iron-icon>
          </button>
        </li>
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("cut")}
          >
            <iron-icon icon="vaadin:scissors"></iron-icon>
          </button>
        </li>
        <li>
          <button
            ?disabled="${!this.hasSelection}"
            @click=${() => this._emit("copy")}
          >
            <iron-icon icon="vaadin:copy-o"></iron-icon>
          </button>
        </li>
      </ul>
    `;
    }
};
WexDitamapTreeToolbar.styles = css `
    .toolbar {
      list-style: none;
      padding: 0;
      display: flex;
      flex-direction: row;
    }
    .toolbar button {
      background: none;
      border: 1px solid;
    }

    iron-icon {
      --iron-icon-width: 1rem;
    }
  `;
__decorate([
    property({ type: Boolean })
], WexDitamapTreeToolbar.prototype, "hasSelection", void 0);
WexDitamapTreeToolbar = __decorate([
    customElement("wex-ditamap-tree-toolbar")
], WexDitamapTreeToolbar);
//# sourceMappingURL=wex-ditamap-tree-toolbar.js.map