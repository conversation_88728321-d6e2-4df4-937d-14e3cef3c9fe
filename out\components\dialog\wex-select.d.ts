import { LitElement } from "lit";
import "../common/dialog";
export declare class WexDialogSelect extends LitElement {
    dialogOpenData: any;
    state: any;
    string: any;
    subscription: any;
    actions: any[];
    selected: any[];
    unselected: any[];
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    private init;
    private stateChange;
    private onConfirm;
    private handlePickListUpdated;
    protected render(): import("lit").TemplateResult<1>;
}
