export declare const PHONES_TREE: DitamapNodeStructure;
export declare const PHONES_TREE_WITH_NOISE: DitamapNodeStructure;
interface DitamapNodeStructure {
    type: string;
    title: string;
    containingMap?: string;
    mapPath: string[];
    href: string;
    rootElementName: string;
    children: DitamapNodeStructure[];
    isTestData?: boolean;
}
export {};
//# sourceMappingURL=json-trees.d.ts.map