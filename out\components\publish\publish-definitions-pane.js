var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// import * as wexlib from "lib/wexlib.js";
import "../base/col-item";
import "../base/row-item";
import "../base/icon";
import "../wex-table";
import "./definitions-filter-bar";
import "./definitions-action-bar";
import "@ui5/webcomponents/dist/Button.js";
let WexPublishDefinitionsPane = class WexPublishDefinitionsPane extends LitElement {
    constructor() {
        super(...arguments);
        this.state = null;
        this.string = null;
        this.projects = [];
        this.pubDefs = [];
        this._isLoading = false;
        this.activeFilters = {};
        this.filterOptions = {};
        this.subscription = null;
        this.pubDefColumns = [];
        this.categories = [];
        this._selectedProjectId = null;
        this._selectedPubDefId = null;
        this._handleSelectAllDefinitions = () => {
            const rows = this.pubDefs.map((row) => ({ ...row, selected: true }));
            this.pubDefs = rows;
        };
        this._handleClearSelectedDefinitions = () => {
            const rows = this.pubDefs.map((row) => ({ ...row, selected: false }));
            this.pubDefs = rows;
        };
    }
    static get styles() {
        return css `
      :host {
        display: flex;
        flex: 1;
        width: 100%;
      }
    `;
        // max-height: 80%;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this._init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        // Update reactive properties from state so Lit notices changes
        this.pubDefs = state._state?.pages?.publish?.pubDefs || [];
        this.projects = state._state?.pages?.publish?.projects || [];
    }
    async _init() {
        try {
            this.pubDefColumns = this.state.columns.publishDefs;
            this.projects = await storeInstance.waitFor("_state.pages.publish.projects");
            this.pubDefs = await storeInstance.waitFor("_state.pages.publish.pubDefs");
        }
        catch (error) {
            console.error("Error during initialization:", error);
        }
    }
    updated(changedProps) {
        if (changedProps.has("pubDefs")) {
            if (this.pubDefs.filter((pubDef) => pubDef.selected).length === 1) {
                this.dispatchEvent(new CustomEvent("single-pubdef-selected", {
                    detail: this.pubDefs.find((pubDef) => pubDef.selected),
                    bubbles: true,
                    composed: true,
                }));
            }
            else {
                this.dispatchEvent(new CustomEvent("reset-selected-pubdef", {
                    bubbles: true,
                    composed: true,
                }));
            }
        }
    }
    async _handleDefRowClick(e) {
        try {
            this._isLoading = true;
            const data = e.detail;
            if (!data)
                return;
            const obj = { ...data.value };
            const rows = [...this.pubDefs];
            if (e.detail.ctrlKey) {
                rows.forEach((row) => {
                    row.selected = row.selected;
                    if (row.pubDefId == obj.pubDefId)
                        row.selected = !row.selected;
                });
                this.pubDefs = rows;
                this._selectedPubDefId = null;
                return;
            }
            rows.forEach((row) => {
                row.selected = row.pubDefId == obj.pubDefId;
            });
            this._selectedPubDefId = rows.find((row) => row.selected).pubDefId;
            this.pubDefs = rows;
        }
        catch (err) {
            console.error("error", err);
        }
        finally {
            this._isLoading = false;
            const selectedPubDefs = this.pubDefs.filter((pubDef) => pubDef.selected);
            if (selectedPubDefs.length === 1) {
                this._selectedProjectId = selectedPubDefs[0].projectId;
            }
            this.requestUpdate();
        }
    }
    // combo bar
    _renderComboBar() {
        if (this.pubDefs.filter((row) => row.selected).length) {
            return html `<wex-publish-definitions-action-bar
        .projects=${this.projects}
        .pubDefs=${this.pubDefs}
        @select-all-definitions=${this._handleSelectAllDefinitions}
        @clear-selected-definitions=${this._handleClearSelectedDefinitions}
      ></wex-publish-definitions-action-bar>`;
            // } else if (this.pubDefs.length) {
            //   return html`<wex-publish-definitions-filter-bar
            //     .pubDefs=${this.pubDefs}
            //     .projects=${this.projects}
            //     .categories=${this.categories}
            //     @set-active-filters=${(e) =>
            //       (this.activeFilters = e.detail.activeFilters)}
            //   ></wex-publish-definitions-filter-bar>`;
        }
        else {
            // console.error("Cannot render combo bar");
            return html `<wex-publish-definitions-filter-bar
        .pubDefs=${this.pubDefs}
        .projects=${this.projects}
        .categories=${this.categories}
        @set-active-filters=${this._handleSetActiveFilters}
      ></wex-publish-definitions-filter-bar>`;
            // @set-active-filters=${(e) =>
            //   (this.activeFilters = e.detail.activeFilters)}
        }
    }
    _handleSetActiveFilters(e) {
        console.log("_handleSetActiveFilters: e.detail", e.detail);
        const { activeFilters } = e.detail;
        // console.log("_handleSetActiveFilters: activeFilters", activeFilters);
        this.activeFilters = activeFilters;
        console.log("_handleSetActiveFilters: this.activeFilters", this.activeFilters);
    }
    // definitions table
    _renderDefsTable() {
        return html `
      <wex-table
        id="publish-defs-table"
        defaultHeight="75vh"
        .activeFilters=${this.activeFilters}
        .columns="${this.pubDefColumns}"
        .rows="${this.pubDefs}"
        @click=${this._handleDefRowClick.bind(this)}
      ></wex-table>
    `;
    }
    render() {
        return html `
      <wex-col-item justifyContent="flex-start">
        ${this._renderComboBar()} ${this._renderDefsTable()}
      </wex-col-item>
    `;
    }
};
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "state", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "string", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "projects", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "pubDefs", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "_isLoading", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "activeFilters", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "filterOptions", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "pubDefColumns", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "categories", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "_selectedProjectId", void 0);
__decorate([
    state()
], WexPublishDefinitionsPane.prototype, "_selectedPubDefId", void 0);
WexPublishDefinitionsPane = __decorate([
    customElement("wex-publish-definitions-pane")
], WexPublishDefinitionsPane);
export { WexPublishDefinitionsPane };
//# sourceMappingURL=publish-definitions-pane.js.map