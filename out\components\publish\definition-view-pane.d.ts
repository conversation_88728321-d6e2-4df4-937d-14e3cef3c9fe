import { LitElement } from "lit";
import "../base/col-item";
import "../base/row-item";
export declare class WexDefinitionViewPane extends LitElement {
    selectedProjectId: number | null;
    selectedPubDefId: number | null;
    pubDef: any;
    isLoading: boolean;
    private state;
    private string;
    private subscription;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    private stateChange;
    protected updated(changedProps: Map<string | number | symbol, unknown>): Promise<void>;
    private init;
    private renderLoading;
    private closePubDef;
    private handleChange;
    private editPubContent;
    private editPubLangs;
    private editPubOutputs;
    private renderCloseButton;
    private renderProjectName;
    private renderContent;
    private renderLanguages;
    private renderOutputs;
    private renderPubDef;
    private renderDefinitionView;
    protected render(): import("lit").TemplateResult<1>;
}
