export class DitamapUtils {
  /**
   * Checks if an html element is a mapref or part
   * @param element
   */
  static isMapRefLike(element: HTMLElement): boolean {
    const hasDitamapFormat = element.getAttribute("format") == "ditamap";
    const hasHref = element.getAttribute("href");
    return Boolean(hasDitamapFormat && hasHref);
  }

  /**
   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
   * @param element
   */
  static isTopicRefLike(element: HTMLElement) {
    if (
      element.tagName.toLowerCase() === "part" ||
      element.tagName.toLowerCase() === "mapref" ||
      element.tagName.toLowerCase() === "bookmap" ||
      element.tagName.toLowerCase() === "map"
    )
      return false;

    //add support for "mapgroup-d/topichead" with no
    var hasHref = false;
    var notExternalScope = false;
    var hasTopicRef = false;
    var hasTopicHead = false;
    var specialCBCase = false;
    var notKeydef = true;
    var notResourceOnly = true;

    if (element.classList) {
      hasTopicRef = element.classList.contains("map/topicref");
      hasTopicHead = element.classList.contains("mapgroup-d/topichead");
    }

    notResourceOnly = element.getAttribute("resource-only") != "resource-only";
    notKeydef = element.tagName.toLowerCase() != "keydef";

    hasHref = element.getAttribute("href") != null;

    if (element.nodeName == "topicref" || element.nodeName == "chapter") {
      if (!hasHref) {
        specialCBCase = true;
      }
    }

    notExternalScope = element.getAttribute("scope") != "external";

    if (
      (hasTopicRef &&
        hasHref &&
        notExternalScope &&
        notResourceOnly &&
        notKeydef) ||
      hasTopicHead ||
      specialCBCase
    ) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * Gets the href used to identify the map, if no href assume it is the root map
   * @param element - xml element
   * @param rootMapName - name of the root map
   */
  static getMapName(
    element: HTMLElement,
    rootMapName?: string
  ): string | undefined {
    if (element.getAttribute("href")) {
      return element.getAttribute("href") as string;
    }

    if (
      rootMapName &&
      (element.tagName.toLowerCase() === "bookmap" ||
        element.tagName.toLowerCase() === "map")
    ) {
      return rootMapName;
    }

    return undefined;
  }

  static getMapTitle(element: HTMLElement) {
    return DitamapUtils.removeXmlNoise(
      DitamapUtils.getDirtyTitle(element)
    ).trim();
  }

  static getDirtyTitle(element: HTMLElement) {
    let mainbooktitleEl = element.querySelector("mainbooktitle");
    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;

    let titleEl = element.querySelector("title");
    if (titleEl) return titleEl.innerText || titleEl.innerHTML;

    let titleAtt = element.getAttribute("title");
    if (titleAtt) return titleAtt;

    return element.tagName;
  }

  static removeXmlNoise(str: string) {
    if (str)
      return str.replace(/<\?xm-replace_text\s+Main Book Title\s*\?>/g, "");
    return str;
  }
}
