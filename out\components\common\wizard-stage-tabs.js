var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit-element";
import { customElement, property, state } from "lit/decorators.js";
let WexWizardStageTabs = class WexWizardStageTabs extends LitElement {
    constructor() {
        super(...arguments);
        this.stages = {};
        this.stage = "basic";
    }
    static get styles() {
        return css `
      .tab {
        display: flex;
        align-items: center;
        padding: var(--spacing-sm) 0;
        cursor: pointer;
        width: fit-content;
        color: var(--color-text);
      }
      .tab[active] {
        border-bottom: 2px solid;
        font-weight: bold;
      }
      .tab:not([active]):hover {
        background-color: var(--clr-white);
        color: var(--primary);
      }
      .tab-btn:disabled {
        pointer-events: none;
        color: #b1b0b0;
      }

      .stage-tabs {
        padding: 0;
        margin: 0;
        width: 10rem;
      }

      .tab-btn {
        background: none;
        border: none;
      }
    `;
    }
    renderStageTitles() {
        return Object.values(this.stages).map((stage) => {
            return html `<li class="tab" ?active=${stage.name === this.stage}>
        <button
          class="tab-btn"
          @click=${() => (this.stage = stage.name)}
          ?disabled=${this.stage != stage.name}
          role="tab"
        >
          ${stage.title}
        </button>
      </li>`;
        });
    }
    render() {
        return html `
      <ul class="stage-tabs">
        ${this.renderStageTitles()}
      </ul>
    `;
    }
};
__decorate([
    property({ type: Object })
], WexWizardStageTabs.prototype, "stages", void 0);
__decorate([
    state()
], WexWizardStageTabs.prototype, "stage", void 0);
WexWizardStageTabs = __decorate([
    customElement("wex-wizard-stage-tabs")
], WexWizardStageTabs);
export { WexWizardStageTabs };
//# sourceMappingURL=wizard-stage-tabs.js.map