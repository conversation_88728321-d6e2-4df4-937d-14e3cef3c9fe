import { FileMeta } from "@bds/types";
import { DitamapContext } from "./ditamap-tree";
export interface DitamapNode {
    /** Unique identifier for the node */
    id: string;
    /** Display title for the node */
    title: string;
    /** Name of the containing map */
    containingMap: string | null;
    /** Path hierarchy to this node */
    mapPath: string[];
    /** Reference to the target resource */
    href: string | null;
    /** Child nodes */
    children: DitamapNode[];
    /** Type of the node */
    type: string;
    /** Context for the tree */
    context: DitamapContext;
    /** Reference to element inserted to map: mapref, part, topicref */
    refElement: HTMLElement | null;
    /** Reference to element resolved by the href: map, bookmap */
    structuralElement: HTMLElement | null;
    /**
     * Converts a file to a ref element that can be inserted into the map.
     */
    _fileToRefElement: (file: FileMeta) => HTMLElement;
    insertNodeFromFile: (file: FileMeta) => void;
    remove: () => void;
}
/**
 * Root node of the ditamap tree. E.g bookmap, map
 */
export declare class RootMapNode implements DitamapNode {
    href: string;
    containingMap: string | null;
    refElement: null;
    structuralElement: HTMLElement;
    mapPath: string[];
    title: string;
    children: DitamapNode[];
    id: string;
    context: DitamapContext;
    constructor({ rootElement, rootMapName, context, }: {
        rootElement: HTMLElement;
        rootMapName: string;
        context?: DitamapContext;
    });
    get type(): string;
    _insert(element: HTMLElement): void;
    _fileToRefElement(file: FileMeta): HTMLElement;
    insertNodeFromFile(file: FileMeta): void;
}
/**
 * References a ditamap - e.g part, mapref
 */
export declare class MapReferenceNode implements DitamapNode {
    href: string;
    containingMap: string;
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    id: string;
    context?: DitamapContext;
    constructor({ refElement, structuralElement, containingMap, context, }: {
        refElement: HTMLElement;
        structuralElement: HTMLElement;
        containingMap: string;
        context?: DitamapContext;
    });
    get title(): string;
    get type(): string;
    _fileToRefElement(file: FileMeta): HTMLElement;
    insertNodeFromFile(file: FileMeta): void;
}
/**
 * Refecences topics - e.g topicref, chapter, appendix, glossref.
 */
export declare class TopicReferenceNode implements DitamapNode {
    href: string;
    containingMap: string;
    refElement: HTMLElement;
    structuralElement: null;
    mapPath: string[];
    children: DitamapNode[];
    context: DitamapContext;
    id: string;
    constructor({ refElement, containingMap, context, }: {
        refElement: HTMLElement;
        containingMap: string;
        context: DitamapContext;
    });
    get title(): string;
    get type(): string;
    _fileToRefElement(file: FileMeta): HTMLElement;
    insertNodeFromFile(file: FileMeta): void;
    remove(): void;
    cut(): void;
}
/**
 * Provides structure to the map. E.g topichead, topicgroup
 */
export declare class StructuralNode implements DitamapNode {
    containingMap: string;
    refElement: null;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    id: string;
    constructor(structuralElement: HTMLElement, containingMap: string);
    get title(): string;
    get type(): string;
    get href(): string;
    _fileToRefElement(file: FileMeta): HTMLElement;
    insertNodeFromFile(file: FileMeta): void;
}
