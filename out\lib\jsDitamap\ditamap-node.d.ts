export interface DitamapNode {
    /** Unique identifier for the node */
    /** Display title for the node */
    title: string;
    /** Name of the containing map */
    containingMap: string | null;
    /** Path hierarchy to this node */
    mapPath: string[];
    /** Reference to the target resource */
    href: string;
    children: DitamapNode[];
    refElement: HTMLElement | null;
    structuralElement: HTMLElement;
}
export declare class RootMapNode implements DitamapNode {
    href: string;
    containingMap: string | null;
    refElement: null;
    structuralElement: HTMLElement;
    mapPath: string[];
    title: string;
    children: DitamapNode[];
    constructor(rootElement: HTMLElement, rootMapName: string);
}
export declare class MapRefLikeElementNode implements DitamapNode {
    href: string;
    containingMap: string;
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    title: string;
    constructor(refElement: HTMLElement, structuralElement: HTMLElement, containingMap: string);
}
/**
 * Content elements reference content and provide structure.
 */
export declare class ContentNode implements DitamapNode {
    href: string;
    containingMap: string | null;
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    title: string;
    constructor(refElement: HTMLElement, containingMap: string);
}
//# sourceMappingURL=ditamap-node.d.ts.map