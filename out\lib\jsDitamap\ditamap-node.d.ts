export interface DitamapNode {
    /** Unique identifier for the node */
    /** Display title for the node */
    title: string;
    /** Name of the containing map */
    containingMap: string | null;
    /** Path hierarchy to this node */
    mapPath: string[];
    /** Reference to the target resource */
    href: string;
    tagName: string;
    children: DitamapNode[];
    refElement: HTMLElement | null;
    structuralElement: HTMLElement;
}
export declare class RootMapNode implements DitamapNode {
    href: string;
    containingMap: string | null;
    refElement: null;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    constructor(rootElement: HTMLElement, rootMapName: string);
    get title(): string;
    get tagName(): string;
}
export declare class MapRefLikeElementNode implements DitamapNode {
    href: string;
    containingMap: string;
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    constructor(refElement: HTMLElement, structuralElement: HTMLElement, containingMap: string);
    get title(): string;
    get tagName(): string;
}
/**
 * Content elements reference content and provide structure.
 */
export declare class ContentNode implements DitamapNode {
    href: string;
    containingMap: string | null;
    refElement: HTMLElement;
    structuralElement: HTMLElement;
    mapPath: string[];
    children: DitamapNode[];
    constructor(refElement: HTMLElement, containingMap: string);
    get title(): string;
    get tagName(): string;
}
//# sourceMappingURL=ditamap-node.d.ts.map