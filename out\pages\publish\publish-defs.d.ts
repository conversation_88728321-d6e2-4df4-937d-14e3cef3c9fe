import { LitElement } from "lit";
import "@vaadin/vaadin-split-layout";
import "../../components/base/row-item";
import "../../components/common/mainpane";
import "../../components/publish/publish-definitions-pane";
import "../../components/publish/definition-view-pane";
import "../../components/header/publish";
export declare class WexPagePublishDefs extends LitElement {
    selectedProjectId: number | null;
    selectedPubDefId: number | null;
    private state;
    private string;
    private subscription;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    stateChange(state: any): void;
    _init(): Promise<void>;
    _handleSinglePubDefSelected(e: CustomEvent): void;
    _handleResetSelectedPubDef(): void;
    _renderPubDefsPane(): import("lit").TemplateResult<1>;
    _renderDefViewPane(): import("lit").TemplateResult<1>;
    _renderMainPane(): import("lit").TemplateResult<1>;
    render(): import("lit").TemplateResult<1>;
}
