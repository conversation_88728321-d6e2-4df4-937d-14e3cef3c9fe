{"version": 3, "file": "ditamap-tree_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap-tree_test.ts"], "names": [], "mappings": "AAUA,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC5B,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C,gBAAgB,EAChB,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC1C,aAAa,EACb,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAElB,IAAI,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC/C,SAAS,CAAC,GAAG,CAAC,4CAA4C,EAAE,OAAO,CAAC,CAAC;QACrE,SAAS,CAAC,GAAG,CAAC,2CAA2C,EAAE,MAAM,CAAC,CAAC;QAEnE,IAAI,IAAI,GAAG,IAAI,WAAW,CACxB,4CAA4C,EAC5C,SAAS,CACV,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAChE,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;YACnB,MAAM;YACN,UAAU;YACV,mBAAmB;SACpB,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,SAAS,GAAG,CAChB,OAA2B,EAC3B,MAAmB,EACnB,EAAE;IACF,IAAI,CAAC,OAAO;QAAE,OAAO,IAAI,CAAC;IAE1B,IAAI,MAAM,GAAM,EAAO,CAAC;IAExB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS;YAAE,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5E,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["/**\r\n * The ditamap editor involves many parts\r\n *  1. Storing a hashmap of the files\r\n *  2. Building a JSON tree from the xml\r\n *  3. Displaying the tree\r\n *  4. Editing the tree\r\n *  5. Saving the tree\r\n *  6. Validating the tree\r\n */\r\nimport { DitamapNode } from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { DitamapTree } from \"../../lib/jsDitamap/ditamap-tree\";\r\nimport { PHONES_TREE } from \"./data/json-trees\";\r\nimport { PHONES_1_BOOKMAP, SUBMAP_PHONES } from \"./files/phones_1_bookmap\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Building JSON tree from xml\", () => {\r\n  it(\"builds phones tree\", () => {\r\n    let bookmap = new DOMParser().parseFromString(\r\n      PHONES_1_BOOKMAP,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let submap = new DOMParser().parseFromString(\r\n      SUBMAP_PHONES,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n\r\n    let workspace = new Map<string, HTMLElement>();\r\n    workspace.set(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\", bookmap);\r\n    workspace.set(\"/Content/submap_phones_xi1609_1_1.ditamap\", submap);\r\n\r\n    let tree = new DitamapTree(\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      workspace\r\n    );\r\n\r\n    expect(pruneTree(PHONES_TREE, [\"href\", \"children\"])).to.deep.equal(\r\n      pruneTree(tree.root, [\r\n        \"href\",\r\n        \"children\",\r\n        // \"containingMap\",\r\n      ])\r\n    );\r\n  });\r\n});\r\n\r\nconst pruneTree = <T extends DitamapNode>(\r\n  current: DitamapNode | null,\r\n  fields: (keyof T)[]\r\n) => {\r\n  if (!current) return null;\r\n\r\n  let pruned: T = {} as T;\r\n\r\n  fields.forEach((field) => {\r\n    if (current[field] != undefined) pruned[field] = current[field];\r\n  });\r\n\r\n  pruned.children = current.children.map((child) => pruneTree(child, fields));\r\n  return pruned;\r\n};\r\n"]}