{"version": 3, "file": "publish-defs-create.js", "sourceRoot": "", "sources": ["../../../src/components/dialog/publish-defs-create.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,KAAK,IAAI,MAAM,UAAU,CAAC;AACjC,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAExC,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAE3C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,yBAAyB,CAAC;AACjC,OAAO,uBAAuB,CAAC;AAC/B,OAAO,wBAAwB,CAAC;AAkDzB,IAAM,0BAA0B,GAAhC,MAAM,0BAA2B,SAAQ,UAAU;IAAnD;;QACuB,mBAAc,GAA0B,IAAI,CAAC;QAEhE,UAAK,GAAwB,EAAE,CAAC;QAChC,WAAM,GAAwB,EAAE,CAAC;QAEjC,UAAK,GAAW,EAAE,CAAC;QACnB,eAAU,GAAiB,EAAE,CAAC;QAC9B,aAAQ,GAAc,EAAE,CAAC;QACzB,eAAU,GAAgB,EAAE,CAAC;QAC7B,uBAAkB,GAAiB,EAAE,CAAC;QACtC,qBAAgB,GAAc,EAAE,CAAC;QACjC,uBAAkB,GAAgB,EAAE,CAAC;QACrC,WAAM,GAAkB,IAAI,CAAC;QAC7B,aAAQ,GAAW,CAAC,CAAC,CAAC;QAEtB,oBAAe,GAAmB,IAAI,CAAC;QACvC,sBAAiB,GAAW,CAAC,CAAC,CAAC;QAC/B,wBAAmB,GAAW,EAAE,CAAC;QACjC,wBAAmB,GAAW,EAAE,CAAC;QACjC,UAAK,GAAW,EAAE,CAAC;QACnB,UAAK,GAAW,EAAE,CAAC;QAEpB,iBAAY,GAAwB,IAAI,CAAC;QACzC,gBAAW,GAAa,EAAE,CAAC;QAC3B,iBAAY,GAAG,OAAO,CAAC;QACd,WAAM,GAAG;YACxB,KAAK,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,iBAAiB;gBACxB,YAAY,EAAE,MAAM;gBACpB,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACpC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;aAChC;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,qBAAqB;gBAC5B,YAAY,EAAE,MAAM;gBACpB,gEAAgE;gBAChE,2CAA2C;gBAC3C,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACpC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;aAChC;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,WAAW;gBAClB,YAAY,EAAE,MAAM;gBACpB,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACpC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;aAChC;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,gBAAgB;gBACvB,YAAY,EAAE,MAAM;gBACpB,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACpC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;aAChC;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,UAAU;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,YAAY,EAAE,MAAM;gBACpB,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE;gBACnC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;aAChC;SACO,CAAC;QACH,SAAI,GAAkB,IAAI,CAAC;QAC3B,YAAO,GAAW;YACxB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC;QA4PM,aAAQ,GAAG,KAAK,IAAI,EAAE;YAC5B,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;gBACnB,KAAK,OAAO;oBACV,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBACtC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBACxD,CAAC;oBAED,IACE,IAAI,CAAC,cAAc,EAAE,IAAI,KAAK,QAAQ;wBACtC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EACrC,CAAC;wBACD,IAAI,CAAC,OAAO,CAAC,IAAI;4BACf,qDAAqD,CAAC;oBAC1D,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBACtC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBACxD,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,EAAE,CAAC;wBAC5D,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,6BAA6B,CAAC;oBACvD,CAAC;oBAED,IACE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;wBACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;wBACnB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,CAAC;wBACD,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;gBACd,KAAK,SAAS;oBACZ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;wBACpC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,sCAAsC,CAAC;oBACjE,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,OAAO,GAAG;4BACb,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,IAAI;yBACf,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;wBAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,KAAK,WAAW;oBACd,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;wBAClC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,sCAAsC,CAAC;oBACjE,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,OAAO,GAAG;4BACb,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,IAAI;yBACf,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;wBAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,KAAK,SAAS;oBACZ,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;wBACpC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,sCAAsC,CAAC;oBACjE,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,OAAO,GAAG;4BACb,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE,IAAI;4BACb,QAAQ,EAAE,IAAI;yBACf,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;wBAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,oCAAoC;wBACpC,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,mCAAmC;oBACnC,OAAO,IAAI,CAAC;gBACd;oBACE,IAAI,CAAC,OAAO,GAAG;wBACb,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,IAAI;wBACb,QAAQ,EAAE,IAAI;qBACf,CAAC;oBACF,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;QAEM,YAAO,GAAG,KAAK,IAAI,EAAE;YAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,yCAAyC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,SAAS,EAAE,IAAI,CAAC,iBAAiB;gBACjC,UAAU,EAAE,IAAI,CAAC,kBAAkB;gBACnC,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,UAAU,EAAE,IAAI,CAAC,kBAAkB;gBACnC,QAAQ,EAAE,CAAC,CAAC;aACb,CAAC;YAEF,sCAAsC;YAEtC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,QAAQ,CAAC;gBACrD,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,UAAU,CACb,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,yCAAyC,CAAC,CACvD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,CAAC,UAAU,CACb,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,yCAAyC,CAAC,CACvD,CAAC;YACJ,CAAC;YAED,sBAAsB;YACtB,yCAAyC;YACzC,qBAAqB;YACrB,sBAAsB;YACtB,OAAO;YACP,KAAK;YACL,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,mBAAmB;YAEhC,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;YAC7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE;oBACnC,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC,CAAC,yBAAyB;QACxC,CAAC,CAAC;QAEM,0BAAqB,GAAG,CAC9B,CAAsD,EACtD,KAAa,EACb,EAAE;YACF,6CAA6C;YAC7C,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,SAAS;oBACZ,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;oBAC5C,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;oBACtC,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;oBAC1C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;oBACpC,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;oBAC5C,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;oBACtC,MAAM;gBACR;oBACE,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC;IA6LJ,CAAC;IAjmBC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;KAsBT,CAAC;IACJ,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CACzC,CAAC,KAA0B,EAAE,EAAE;YAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAES,OAAO,CAAC,iBAAyD;QACzE,8CAA8C;QAE9C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;QAC5D,IACE,MAAM;YACN,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACvC,IAAI,CAAC,cAAc,EACnB,CAAC;YACD,yCAAyC;YACzC,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IACE,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,CAAC;YAC1C,IAAI,CAAC,iBAAiB,IAAI,CAAC,EAC3B,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAA0B;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,+BAA+B,CAAC;QAC5D,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,wBAAwB;YACxB,sBAAsB;YACtB,wBAAwB;QAC1B,CAAC;IACH,CAAC;IAEO,OAAO;QACb,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YACpD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;QAC7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACnC,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;QAE3C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3E,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACnD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CACjC,CAAC,OAAmB,EAAE,EAAE,CACtB,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAC5B,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY,CACxD,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;QAC3C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvE,MAAM,gBAAgB,GAAG,MAAM,EAAE,YAAY,CAAC;QAC9C,4DAA4D;QAE5D,sBAAsB;QACtB,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAa,EAAE,EAAE;YACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAa,EAAE,EAAE;gBACzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/B,CAAC,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAC7B,CAAC,IAAa,EAAE,EAAE,CAChB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CACnE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,0FAA0F;IAClF,KAAK,CAAC,aAAa;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;QAC3C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,mBAAmB,EAAE,CAAC;QACtD,mEAAmE;QACnE,kBAAkB;QAClB,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,mBAAmB;QACnB,MAAM;QACN,KAAK;QACL,MAAM,kBAAkB,GAAG,MAAM,EAAE,gBAAgB,CAAC;QAEpD,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,kBAAkB,CAAC,CAAC;QAErE,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CACjC,CAAC,MAAiB,EAAE,EAAE,CACpB,CAAC,kBAAkB,CAAC,IAAI,CACtB,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,YAAY,KAAK,MAAM,CAAC,YAAY,CACvD,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,IAAI;QAChB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,+BAA+B,CAAC;QAClE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,OAAsB,IAAI,EAC1B,CAAqD;QAErD,4CAA4C;QAC5C,IAAI,CAAC;YACH,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;gBACtB,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBACzC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC9B,CAAC;YAED,4DAA4D;QAC9D,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAEO,YAAY,CAAC,IAAc,EAAE,CAAQ;QAC3C,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;QAC5C,MAAM,KAAK,GAAG,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAE1C,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,IAAI,KAAK,MAAM;YAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACxC,IAAI,IAAI,KAAK,MAAM;YAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAExC,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEO,aAAa,CAAC,GAAW;QAC/B,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,CAAC;QAEvB,MAAM,GAAG,GAAG,8BAA8B,CAAC,CAAC,yCAAyC;QACrF,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,4BAA4B;QAClD,MAAM,GAAG,GAAG,yCAAyC,CAAC,CAAC,yBAAyB;QAChF,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IA8KO,oBAAoB;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAA;;;gCAGO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;qBAE/B,IAAI,CAAC,KAAK;sBACT,CAAC,CAAa,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;;;YAGzD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YACnB,CAAC,CAAC,IAAI,CAAA,4BAA4B,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU;YAC7D,CAAC,CAAC,IAAI,CAAA,EAAE;;;;gCAIY,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;;qBAEtC,IAAI,CAAC,KAAK;sBACT,CAAC,CAAa,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;;;YAGzD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YACnB,CAAC,CAAC,IAAI,CAAA,4BAA4B,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU;YAC7D,CAAC,CAAC,IAAI,CAAA,EAAE;;;;gCAIY,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;uBAGhC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;;;cAGvD,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ;YACnD,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAA;;kCAED,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,SAAS;+BAC9C,OAAO,CAAC,SAAS;;wBAExB,OAAO,CAAC,IAAI;;mBAEjB,CACF;YACH,CAAC,CAAC,IAAI;;YAER,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;YACtB,CAAC,CAAC,IAAI,CAAA,4BAA4B,IAAI,CAAC,OAAO,CAAC,OAAO,UAAU;YAChE,CAAC,CAAC,IAAI,CAAA,EAAE;;;KAGf,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,KAAa,EAAE,QAAe,EAAE,UAAiB;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAA;;;uBAGF,KAAK;sBACN,QAAQ;wBACN,UAAU;+BACH,CAAC,CAAc,EAAE,EAAE,CACtC,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,KAAK,CAAC;;;UAGtC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;YACvB,CAAC,CAAC,IAAI,CAAA,4BAA4B,IAAI,CAAC,OAAO,CAAC,QAAQ,UAAU;YACjE,CAAC,CAAC,IAAI,CAAA,EAAE;;KAEb,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,KAAa,EAAE,QAAe,EAAE,UAAiB;QACxE,gEAAgE;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAA;;;uBAGF,KAAK;sBACN,QAAQ;wBACN,UAAU;+BACH,CAAC,CAAc,EAAE,EAAE,CACtC,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,KAAK,CAAC;;;UAGtC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;YACvB,CAAC,CAAC,IAAI,CAAA,4BAA4B,IAAI,CAAC,OAAO,CAAC,QAAQ,UAAU;YACjE,CAAC,CAAC,IAAI,CAAA,EAAE;;KAEb,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,kBAAkB;QACxB,OAAO,CAAC,GAAG,CACT,6CAA6C,EAC7C,IAAI,CAAC,kBAAkB,CACxB,CAAC;QACF,OAAO,IAAI,CAAC,iBAAiB,CAC3B,SAAS,EACT,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAEO,oBAAoB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAC1B,WAAW,EACX,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAC1B,SAAS,EACT,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAEO,GAAG;QACT,MAAM,IAAI,GAAG;YACX,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,SAAS,EAAE,IAAI,CAAC,iBAAiB;YACjC,UAAU,EAAE,IAAI,CAAC,kBAAkB;YACnC,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,UAAU,EAAE,IAAI,CAAC,kBAAkB;YACnC,QAAQ,EAAE,CAAC,CAAC;SACb,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,QAAQ,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IAEO,mBAAmB;QACzB,wCAAwC;QACxC,sCAAsC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAA;;6BAEI,IAAI,CAAC,GAAG;;;KAGhC,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,UAAU;QAChB,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACrC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACnC,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACrC,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACnC,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAEpC;gBACE,OAAO,IAAI,CAAA,gBAAgB,IAAI,CAAC,KAAK,SAAS,CAAC;QACnD,CAAC;IACH,CAAC;IAES,MAAM;QACd,MAAM,QAAQ,GAAG,IAAI,CAAA;;gBAET,CAAC,CAAC,IAAI,CAAC,cAAc;;eAEtB,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,EAAE;;;kBAG5B,IAAI,CAAC,MAAM;;;4BAGD,CAAC,CAAc,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;;UAE7D,IAAI,CAAC,UAAU,EAAE;;KAEtB,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAzqB6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kEAA8C;AAEhE;IAAR,KAAK,EAAE;yDAAiC;AAChC;IAAR,KAAK,EAAE;0DAAkC;AAEjC;IAAR,KAAK,EAAE;yDAAoB;AACnB;IAAR,KAAK,EAAE;8DAA+B;AAC9B;IAAR,KAAK,EAAE;4DAA0B;AACzB;IAAR,KAAK,EAAE;8DAA8B;AAC7B;IAAR,KAAK,EAAE;sEAAuC;AACtC;IAAR,KAAK,EAAE;oEAAkC;AACjC;IAAR,KAAK,EAAE;sEAAsC;AACrC;IAAR,KAAK,EAAE;0DAA8B;AAC7B;IAAR,KAAK,EAAE;4DAAuB;AAEtB;IAAR,KAAK,EAAE;mEAAwC;AACvC;IAAR,KAAK,EAAE;qEAAgC;AAC/B;IAAR,KAAK,EAAE;uEAAkC;AACjC;IAAR,KAAK,EAAE;uEAAkC;AACjC;IAAR,KAAK,EAAE;yDAAoB;AACnB;IAAR,KAAK,EAAE;yDAAoB;AArBjB,0BAA0B;IADtC,aAAa,CAAC,gCAAgC,CAAC;GACnC,0BAA0B,CA0qBtC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\nimport * as util from \"lib/util\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/Label.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\nimport \"@ui5/webcomponents/dist/Select.js\";\r\nimport \"@ui5/webcomponents/dist/Option.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../common/wizard-dialog\";\r\nimport \"../common/list-picker\";\r\nimport \"../common/table-picker\";\r\n\r\ninterface DialogOpenData {\r\n  mode?: string;\r\n  pubDef?: any;\r\n  projects?: any[];\r\n}\r\n\r\ninterface PubContent {\r\n  pubContentId: number;\r\n  name: string;\r\n  [key: string]: any;\r\n}\r\n\r\ninterface PubLang {\r\n  langId: number;\r\n  description: string;\r\n  name?: string; // used for picker\r\n}\r\n\r\ninterface PubOutput {\r\n  outputTypeId: number;\r\n  name: string;\r\n}\r\n\r\ninterface PubDef {\r\n  name: string;\r\n  desc: string;\r\n  projectId: number;\r\n  pubDefId: number;\r\n  lstPubContentDtos: PubContent[];\r\n  lstLanguages: PubLang[];\r\n  lstOutputFormats: PubOutput[];\r\n}\r\n\r\ninterface Project {\r\n  name: string;\r\n  projectId: number;\r\n}\r\n\r\ninterface Errors {\r\n  name: string | null;\r\n  desc: string | null;\r\n  project: string | null;\r\n  selected: string | null;\r\n}\r\n\r\ntype ErrorKey = keyof Errors;\r\n\r\n@customElement(\"wex-dialog-publish-defs-create\")\r\nexport class WexDialogPublishDefsCreate extends LitElement {\r\n  @property({ type: Object }) dialogOpenData: DialogOpenData | null = null;\r\n\r\n  @state() state: Record<string, any> = {};\r\n  @state() string: Record<string, any> = {};\r\n\r\n  @state() stage: string = \"\";\r\n  @state() pubContent: PubContent[] = [];\r\n  @state() pubLangs: PubLang[] = [];\r\n  @state() pubOutputs: PubOutput[] = [];\r\n  @state() selectedPubContent: PubContent[] = [];\r\n  @state() selectedPubLangs: PubLang[] = [];\r\n  @state() selectedPubOutputs: PubOutput[] = [];\r\n  @state() pubDef: PubDef | null = null;\r\n  @state() pubDefId: number = -1;\r\n\r\n  @state() selectedProject: Project | null = null;\r\n  @state() selectedProjectId: number = -1;\r\n  @state() selectedProjectName: string = \"\";\r\n  @state() selectedProjectDesc: string = \"\";\r\n  @state() _name: string = \"\";\r\n  @state() _desc: string = \"\";\r\n\r\n  private subscription: (() => void) | null = null;\r\n  private pubDefNames: string[] = [];\r\n  private initialStage = \"basic\";\r\n  private readonly stages = {\r\n    basic: {\r\n      name: \"basic\",\r\n      title: \"Definition Name\",\r\n      confirmLabel: \"Next\",\r\n      confirmAction: () => this.validate(),\r\n      cancelAction: () => this.init(),\r\n    },\r\n    content: {\r\n      name: \"content\",\r\n      title: \"Publication Content\",\r\n      confirmLabel: \"Next\",\r\n      // confirmAction: () => this.confirmPubContent(this.pubContent),\r\n      // confirmAction: () => this.setPubLangs(),\r\n      confirmAction: () => this.validate(),\r\n      cancelAction: () => this.init(),\r\n    },\r\n    languages: {\r\n      name: \"languages\",\r\n      title: \"Languages\",\r\n      confirmLabel: \"Next\",\r\n      confirmAction: () => this.validate(),\r\n      cancelAction: () => this.init(),\r\n    },\r\n    outputs: {\r\n      name: \"outputs\",\r\n      title: \"Output Formats\",\r\n      confirmLabel: \"Next\",\r\n      confirmAction: () => this.validate(),\r\n      cancelAction: () => this.init(),\r\n    },\r\n    filename: {\r\n      name: \"filename\",\r\n      title: \"Output File Name\",\r\n      confirmLabel: \"Save\",\r\n      confirmAction: () => this.confirm(),\r\n      cancelAction: () => this.init(),\r\n    },\r\n  } as const;\r\n  private mode: string | null = null;\r\n  private _errors: Errors = {\r\n    name: null,\r\n    desc: null,\r\n    project: null,\r\n    selected: null,\r\n  };\r\n\r\n  static get styles() {\r\n    return css`\r\n      wex-row-item {\r\n        color: var(--font-color);\r\n        margin-bottom: 1rem;\r\n      }\r\n\r\n      wex-col-item {\r\n        margin-bottom: 1rem;\r\n      }\r\n\r\n      ui5-input {\r\n        width: 100%;\r\n      }\r\n\r\n      ui5-select {\r\n        width: 100%;\r\n      }\r\n\r\n      .error-msg {\r\n        font-style: italic;\r\n        color: var(--clr-negative);\r\n      }\r\n    `;\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe(\r\n      (state: Record<string, any>) => {\r\n        this.stateChange(state);\r\n      }\r\n    );\r\n\r\n    this.init();\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  protected updated(changedProperties: Map<string | number | symbol, unknown>) {\r\n    // console.log(\"type this:\", this.pubContent);\r\n\r\n    const dialog = this.shadowRoot?.querySelector(\"ui5-dialog\");\r\n    if (\r\n      dialog &&\r\n      changedProperties.has(\"dialogOpenData\") &&\r\n      this.dialogOpenData\r\n    ) {\r\n      // console.log(\"dialogOpenData changed\");\r\n      this.init();\r\n    }\r\n\r\n    if (\r\n      changedProperties.has(\"selectedProjectId\") &&\r\n      this.selectedProjectId >= 0\r\n    ) {\r\n      this.setMode();\r\n      this.setPubContent();\r\n      this.setPubLangs();\r\n      this.setPubOutputs();\r\n    }\r\n  }\r\n\r\n  private stateChange(state: Record<string, any>) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.dialogOpenData = state.publish_defs_create_dialog_open;\r\n    if (!!this.dialogOpenData) {\r\n      this.setBasicInfo();\r\n      // this.setPubContent();\r\n      // this.setPubLangs();\r\n      // this.setPubOutputs();\r\n    }\r\n  }\r\n\r\n  private setMode() {\r\n    if (this.dialogOpenData && this.dialogOpenData.mode) {\r\n      this.mode = this.dialogOpenData.mode;\r\n    }\r\n  }\r\n\r\n  private async setBasicInfo() {\r\n    const pubDef = this.dialogOpenData?.pubDef;\r\n    this._errors = {\r\n      name: null,\r\n      desc: null,\r\n      project: null,\r\n      selected: null,\r\n    };\r\n\r\n    const pubDefs = await wexlib.getAllPubDefs();\r\n    if (pubDefs.length) {\r\n      storeInstance.dispatch(\"setPubDefs\", {\r\n        pubDefs,\r\n      });\r\n    }\r\n\r\n    this.pubDefNames = pubDefs.map((pubDef: PubDef) => pubDef.name);\r\n    if (pubDef) {\r\n      this._name = pubDef.name;\r\n      this._desc = pubDef.desc;\r\n      this.selectedProjectId = pubDef.projectId;\r\n    } else {\r\n      this._name = \"\";\r\n      this._desc = \"\";\r\n      this.selectedProjectId = -1;\r\n    }\r\n  }\r\n\r\n  private async setPubContent() {\r\n    const pubDef = this.dialogOpenData?.pubDef;\r\n\r\n    const pubContent = await wexlib.getProjPubContents(this.selectedProjectId);\r\n    this.pubContent = pubContent;\r\n\r\n    if (pubDef) {\r\n      this.selectedPubContent = pubDef.lstPubContentDtos;\r\n      this.pubContent = pubContent.filter(\r\n        (content: PubContent) =>\r\n          !pubDef.lstPubContentDtos.some(\r\n            (dto: any) => dto.pubContentId === content.pubContentId\r\n          )\r\n      );\r\n    }\r\n  }\r\n\r\n  private async setPubLangs() {\r\n    const pubDef = this.dialogOpenData?.pubDef;\r\n    const pubLangs = await wexlib.getProjLanguages(this.selectedProjectId);\r\n    const selectedPubLangs = pubDef?.lstLanguages;\r\n    // console.log(\"setting langs\", pubLangs, selectedPubLangs);\r\n\r\n    // set name for picker\r\n    pubLangs.forEach((lang: PubLang) => {\r\n      lang.name = lang.description;\r\n    });\r\n    if (selectedPubLangs) {\r\n      selectedPubLangs.forEach((lang: PubLang) => {\r\n        lang.name = lang.description;\r\n      });\r\n    }\r\n    this.pubLangs = pubLangs;\r\n\r\n    if (pubDef) {\r\n      this.selectedPubLangs = selectedPubLangs;\r\n      this.pubLangs = pubLangs.filter(\r\n        (lang: PubLang) =>\r\n          !selectedPubLangs.some((dto: any) => dto.langId === lang.langId)\r\n      );\r\n    }\r\n  }\r\n\r\n  // the engine id needs to be added for each output... pubOutputs seems to have the ID, but\r\n  private async setPubOutputs() {\r\n    const pubDef = this.dialogOpenData?.pubDef;\r\n    const pubOutputs = await wexlib.getPubEngineOutputs();\r\n    // const selectedPubOutputs = pubDef?.lstSelectedOutputFormats.map(\r\n    //   (engine) => {\r\n    //     const test = engine.lstPublishOutputs[0];\r\n    //     console.log(\"inside map: engine\", engine);\r\n    //     console.log(\"inside map: test\", test);\r\n    //     return test;\r\n    //   }\r\n    // );\r\n    const selectedPubOutputs = pubDef?.lstOutputFormats;\r\n\r\n    console.log(\"setPubOutputs: pubDef\", pubDef);\r\n    console.log(\"setPubOutputs: pubOutputs\", pubOutputs);\r\n    console.log(\"setPubOutputs: selectedPubOutputs\", selectedPubOutputs);\r\n\r\n    this.pubOutputs = pubOutputs;\r\n\r\n    if (pubDef) {\r\n      this.selectedPubOutputs = selectedPubOutputs;\r\n      this.pubOutputs = pubOutputs.filter(\r\n        (output: PubOutput) =>\r\n          !selectedPubOutputs.some(\r\n            (dto: any) => dto.outputTypeId === output.outputTypeId\r\n          )\r\n      );\r\n    }\r\n  }\r\n\r\n  private async init() {\r\n    this.dialogOpenData = this.state?.publish_defs_create_dialog_open;\r\n    this.stage = this.initialStage;\r\n    this.selectedProjectId = -1;\r\n    this.selectedPubContent = [];\r\n    this.selectedPubLangs = [];\r\n    this.selectedPubOutputs = [];\r\n    this._name = \"\";\r\n    this._desc = \"\";\r\n  }\r\n\r\n  private async handleProjectSelect(\r\n    type: string | null = null,\r\n    e: CustomEvent<{ selectedOption: { value: number } }>\r\n  ) {\r\n    // console.log(\"project selection changed\");\r\n    try {\r\n      if (!type) return;\r\n\r\n      if (type == \"project\") {\r\n        const id = e.detail.selectedOption.value;\r\n        this.selectedProjectId = id;\r\n        this._errors.project = null;\r\n      }\r\n\r\n      // console.log(\"selected project:\", this.selectedProjectId);\r\n    } catch {}\r\n  }\r\n\r\n  private handleChange(type: ErrorKey, e: Event) {\r\n    console.log(\"handleChange: type\", type);\r\n    console.log(\"handleChange: e\", e);\r\n    if (!type) return;\r\n    const target = e.target as HTMLInputElement;\r\n    const value = target?.value ?? \"\";\r\n    console.log(\"handleChange: value\", value);\r\n\r\n    // this[`_${type}`] = value;\r\n    if (!value.length) {\r\n      this._errors[type] = this.string[\"_error_empty_value\"];\r\n    } else if (!this.isValidString(value)) {\r\n      this._errors[type] = this.string[\"_error_invalid_value\"];\r\n    } else {\r\n      this._errors[type] = null;\r\n    }\r\n\r\n    if (type === \"name\") this._name = value;\r\n    if (type === \"desc\") this._desc = value;\r\n\r\n    this.requestUpdate();\r\n  }\r\n\r\n  private isValidString(str: string) {\r\n    if (!str) return false;\r\n\r\n    const rg1 = /^[^\\\\/:\\*\\?\"<>+%@#&,=~'\\|]+$/; // forbidden characters \\ / : * ? \" < > |\r\n    const rg2 = /^[^\\.]/; // cannot start with dot (.)\r\n    const rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\\.|$)/i; // forbidden folder names\r\n    return rg1.test(str) && rg2.test(str) && !rg3.test(str);\r\n  }\r\n\r\n  private validate = async () => {\r\n    switch (this.stage) {\r\n      case \"basic\":\r\n        if (!this._name || !this._name.length) {\r\n          this._errors.name = this.string[\"_error_empty_value\"];\r\n        }\r\n\r\n        if (\r\n          this.dialogOpenData?.mode === \"Create\" &&\r\n          this.pubDefNames.includes(this._name)\r\n        ) {\r\n          this._errors.name =\r\n            \"A publish definition with this name already exists.\";\r\n        }\r\n\r\n        if (!this._desc || !this._desc.length) {\r\n          this._errors.desc = this.string[\"_error_empty_value\"];\r\n        }\r\n        if (!this.selectedProjectId || this.selectedProjectId == -1) {\r\n          this._errors.project = \"A project must be selected.\";\r\n        }\r\n\r\n        if (\r\n          !!this._errors.name ||\r\n          !!this._errors.desc ||\r\n          !!this._errors.project\r\n        ) {\r\n          this.requestUpdate();\r\n          return false;\r\n        }\r\n\r\n        this.setPubContent();\r\n        return true;\r\n      case \"content\":\r\n        if (!this.selectedPubContent.length) {\r\n          this._errors.selected = \"The selected column cannot be empty.\";\r\n        } else {\r\n          this._errors = {\r\n            name: null,\r\n            desc: null,\r\n            project: null,\r\n            selected: null,\r\n          };\r\n        }\r\n\r\n        if (!!this._errors.selected) {\r\n          this.requestUpdate();\r\n          return false;\r\n        }\r\n        return true;\r\n      case \"languages\":\r\n        if (!this.selectedPubLangs.length) {\r\n          this._errors.selected = \"The selected column cannot be empty.\";\r\n        } else {\r\n          this._errors = {\r\n            name: null,\r\n            desc: null,\r\n            project: null,\r\n            selected: null,\r\n          };\r\n        }\r\n\r\n        if (!!this._errors.selected) {\r\n          this.requestUpdate();\r\n          return false;\r\n        }\r\n        return true;\r\n      case \"outputs\":\r\n        if (!this.selectedPubOutputs.length) {\r\n          this._errors.selected = \"The Selected column cannot be empty.\";\r\n        } else {\r\n          this._errors = {\r\n            name: null,\r\n            desc: null,\r\n            project: null,\r\n            selected: null,\r\n          };\r\n        }\r\n\r\n        if (!!this._errors.selected) {\r\n          this.requestUpdate();\r\n          // console.log(\"validate is false\");\r\n          return false;\r\n        }\r\n        // console.log(\"validate is true\");\r\n        return true;\r\n      default:\r\n        this._errors = {\r\n          name: null,\r\n          desc: null,\r\n          project: null,\r\n          selected: null,\r\n        };\r\n        return true;\r\n    }\r\n  };\r\n\r\n  private confirm = async () => {\r\n    const isValid = await this.validate();\r\n    if (!isValid) {\r\n      // console.log(\"should close?\", isValid);\r\n      return false;\r\n    }\r\n    const body = {\r\n      name: this._name,\r\n      desc: this._desc,\r\n      projectId: this.selectedProjectId,\r\n      pubContent: this.selectedPubContent,\r\n      pubLangs: this.selectedPubLangs,\r\n      pubOutputs: this.selectedPubOutputs,\r\n      pubDefId: -1,\r\n    };\r\n\r\n    // console.log(\"confirm: body\", body);\r\n\r\n    if (this.mode === \"Edit\") {\r\n      body.pubDefId = this.dialogOpenData?.pubDef.pubDefId;\r\n      await wexlib.editPubDef(body);\r\n      util.notifyUser(\r\n        \"positive\",\r\n        this.string[\"_msg_publish_defs_create_pubdef_success\"]\r\n      );\r\n    } else {\r\n      await wexlib.createPubDef(body);\r\n      util.notifyUser(\r\n        \"positive\",\r\n        this.string[\"_msg_publish_defs_create_pubdef_success\"]\r\n      );\r\n    }\r\n\r\n    // this.dispatchEvent(\r\n    //   new CustomEvent(\"update-def-list\", {\r\n    //     bubbles: true,\r\n    //     composed: true,\r\n    //   })\r\n    // );\r\n    this.init(); // reset the dialog\r\n\r\n    // refresh the API data\r\n    const pubDefs = await wexlib.getAllPubDefs();\r\n    if (pubDefs.length) {\r\n      storeInstance.dispatch(\"setPubDefs\", {\r\n        pubDefs,\r\n      });\r\n    }\r\n\r\n    return true; // true closes the dialog\r\n  };\r\n\r\n  private handlePickListUpdated = (\r\n    e: CustomEvent<{ selected: any[]; unselected: any[] }>,\r\n    stage: string\r\n  ) => {\r\n    // console.log(\"handle pick list\", e.detail);\r\n    switch (stage) {\r\n      case \"content\":\r\n        this.selectedPubContent = e.detail.selected;\r\n        this.pubContent = e.detail.unselected;\r\n        break;\r\n      case \"languages\":\r\n        this.selectedPubLangs = e.detail.selected;\r\n        this.pubLangs = e.detail.unselected;\r\n        break;\r\n      case \"outputs\":\r\n        this.selectedPubOutputs = e.detail.selected;\r\n        this.pubOutputs = e.detail.unselected;\r\n        break;\r\n      default:\r\n        console.error(\"error in handlePickListUpdated\");\r\n    }\r\n  };\r\n\r\n  private renderBasicInfoStage() {\r\n    const template = html`\r\n      <wex-col-item>\r\n        <wex-col-item justifyContent=\"flex-start\">\r\n          <ui5-label required>${this.string[\"_name\"]}</ui5-label>\r\n          <ui5-input\r\n            value=\"${this._name}\"\r\n            @input=\"${(e: InputEvent) => this.handleChange(\"name\", e)}\"\r\n          >\r\n          </ui5-input>\r\n          ${!!this._errors.name\r\n            ? html` <span class=\"error-msg\">${this._errors.name}</span> `\r\n            : html``}\r\n        </wex-col-item>\r\n\r\n        <wex-col-item justifyContent=\"flex-start\">\r\n          <ui5-label required>${this.string[\"_description\"]}</ui5-label>\r\n          <ui5-input\r\n            value=\"${this._desc}\"\r\n            @input=\"${(e: InputEvent) => this.handleChange(\"desc\", e)}\"\r\n          >\r\n          </ui5-input>\r\n          ${!!this._errors.desc\r\n            ? html` <span class=\"error-msg\">${this._errors.desc}</span> `\r\n            : html``}\r\n        </wex-col-item>\r\n\r\n        <wex-col-item justifyContent=\"flex-start\">\r\n          <ui5-label required>${this.string[\"_project\"]}</ui5-label>\r\n\r\n          <ui5-select\r\n            @change=\"${this.handleProjectSelect.bind(this, \"project\")}\"\r\n          >\r\n            <ui5-option value=\"-1\"> No project selected </ui5-option>\r\n            ${this.dialogOpenData && this.dialogOpenData.projects\r\n              ? this.dialogOpenData.projects.map(\r\n                  (project) => html`\r\n                    <ui5-option\r\n                      ?selected=${this.selectedProjectId == project.projectId}\r\n                      value=\"${project.projectId}\"\r\n                    >\r\n                      ${project.name}\r\n                    </ui5-option>\r\n                  `\r\n                )\r\n              : null}\r\n          </ui5-select>\r\n          ${!!this._errors.project\r\n            ? html` <span class=\"error-msg\">${this._errors.project}</span> `\r\n            : html``}\r\n        </wex-col-item>\r\n      </wex-col-item>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderTablePicker(stage: string, selected: any[], unselected: any[]) {\r\n    const template = html`\r\n      <wex-col-item>\r\n        <wex-table-picker\r\n          id=\"pubdef-${stage}\"\r\n          .selected=${selected}\r\n          .unselected=${unselected}\r\n          @pick-list-updated=${(e: CustomEvent) =>\r\n            this.handlePickListUpdated(e, stage)}\r\n        ></wex-table-picker>\r\n\r\n        ${!!this._errors.selected\r\n          ? html` <span class=\"error-msg\">${this._errors.selected}</span> `\r\n          : html``}\r\n      </wex-col-item>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderListPicker(stage: string, selected: any[], unselected: any[]) {\r\n    // console.log(\"renderListPicker\", stage, selected, unselected);\r\n    const template = html`\r\n      <wex-col-item>\r\n        <wex-list-picker\r\n          id=\"pubdef-${stage}\"\r\n          .selected=${selected}\r\n          .unselected=${unselected}\r\n          @pick-list-updated=${(e: CustomEvent) =>\r\n            this.handlePickListUpdated(e, stage)}\r\n        ></wex-list-picker>\r\n\r\n        ${!!this._errors.selected\r\n          ? html` <span class=\"error-msg\">${this._errors.selected}</span> `\r\n          : html``}\r\n      </wex-col-item>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderContentStage() {\r\n    console.log(\r\n      \"renderContentStage: this.selectedPubContent\",\r\n      this.selectedPubContent\r\n    );\r\n    return this.renderTablePicker(\r\n      \"content\",\r\n      this.selectedPubContent,\r\n      this.pubContent\r\n    );\r\n  }\r\n\r\n  private renderLanguagesStage() {\r\n    return this.renderListPicker(\r\n      \"languages\",\r\n      this.selectedPubLangs,\r\n      this.pubLangs\r\n    );\r\n  }\r\n\r\n  private renderOutputsStage() {\r\n    return this.renderListPicker(\r\n      \"outputs\",\r\n      this.selectedPubOutputs,\r\n      this.pubOutputs\r\n    );\r\n  }\r\n\r\n  private log() {\r\n    const body = {\r\n      name: this._name,\r\n      desc: this._desc,\r\n      projectId: this.selectedProjectId,\r\n      pubContent: this.selectedPubContent,\r\n      pubLangs: this.selectedPubLangs,\r\n      pubOutputs: this.selectedPubOutputs,\r\n      pubDefId: -1,\r\n    };\r\n\r\n    body.pubDefId = this.dialogOpenData?.pubDef.pubDefId;\r\n\r\n    console.log(\"log: body\", body);\r\n  }\r\n\r\n  private renderFilenameStage() {\r\n    // <ui5-label>Filename Stage</ui5-label>\r\n    // <ui5-input value=\"${this._name}\" />\r\n    const template = html`\r\n      <div>\r\n        <ui5-button @click=${this.log}>Log</ui5-button>\r\n        <p>Click the log button</p>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderBody() {\r\n    switch (this.stage) {\r\n      case \"basic\":\r\n        return this.renderBasicInfoStage();\r\n      case \"content\":\r\n        return this.renderContentStage();\r\n      case \"languages\":\r\n        return this.renderLanguagesStage();\r\n      case \"outputs\":\r\n        return this.renderOutputsStage();\r\n      case \"filename\":\r\n        return this.renderFilenameStage();\r\n\r\n      default:\r\n        return html` <div>Error, ${this.stage}</div> `;\r\n    }\r\n  }\r\n\r\n  protected render() {\r\n    const template = html`\r\n      <wex-wizard-dialog\r\n        .open=${!!this.dialogOpenData}\r\n        headerText=\"Publish Definition\"\r\n        mode=${this.dialogOpenData?.mode || \"\"}\r\n        width=\"50%\"\r\n        height=\"70vh\"\r\n        .stages=${this.stages}\r\n        initialStage=\"basic\"\r\n        dialogOpenStateProperty=\"publish_defs_create_dialog_open\"\r\n        @set-wizard-stage=${(e: CustomEvent) => (this.stage = e.detail)}\r\n      >\r\n        ${this.renderBody()}\r\n      </wex-wizard-dialog>\r\n    `;\r\n    return template;\r\n  }\r\n}\r\n"]}