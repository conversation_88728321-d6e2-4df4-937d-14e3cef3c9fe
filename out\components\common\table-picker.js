var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
import "../wex-table";
// objects in the arrays must have a name key to function correctly
// the primary use case is currently to update values on a pubdef
// emits both selected and unselected on every change
let WexTablePicker = class WexTablePicker extends LitElement {
    constructor() {
        super(...arguments);
        this.selected = [];
        this.unselected = [];
        this.state = {};
        this.string = {};
        this.actions = [];
        this.columns = [];
    }
    static get styles() {
        return css `
      :host {
        display: flex;
        flex: 1;
        width: 100%;
        height: 100%;
        min-height: 0;
        max-height: 100%;
        box-sizing: border-box;
        overflow: hidden;
      }

      /* Ensure the main column container fills the host */
      wex-col-item {
        flex: 1;
        width: 100%;
        height: 100%;
        min-height: 0;
        max-height: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      /* Fixed height tables with light border */
      wex-table {
        height: 140px;
        min-height: 140px;
        max-height: 140px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: auto;
        flex-shrink: 0;
      }

      .italic {
        font-style: italic;
      }

      #dialog {
        width: min(90vw, 600px);
      }

      .dialog-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 1rem;
        height: min(50vh, 300px);
        color: var(--font-color);
      }

      .dialog-content * {
        color: var(--font-color);
      }

      .dialog-content > *:not(:last-child) {
        margin-right: 0.5rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
        max-height: calc(2.25rem * 8);
        height: 30vh;
      }

      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }

      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }

      li > .icon-container:hover {
        background: var(--clr-white);
      }

      li > *:not(:last-child) {
        margin-right: 1rem;
      }

      li[active] {
        background: var(--row-selected-background);
        overflow: hidden;
        text-overflow: ellipsis;
      }

      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .select-column {
        display: flex;
        flex-direction: column;
        width: 100%;
        flex-shrink: 0;
      }

      .select-column > span {
        margin-bottom: 0.25rem;
        font-weight: 500;
        flex-shrink: 0;
      }

      .select-actions {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0.25rem 0;
        gap: 1rem;
        flex-shrink: 0;
      }

      .select-actions > * {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 4px;
        transition: background-color 0.2s;
      }

      .select-actions > *:hover {
        background-color: var(--clr-gray-ultra-light, #f5f5f5);
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this.init(state);
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    init(state) {
        this.actions = [
            { name: "select", label: "_select", icon: "vaadin:angle-down" },
            { name: "deselect", label: "_deselect", icon: "vaadin:angle-up" },
        ];
        this.columns = [
            {
                field: "name",
                label: "_name",
                isSortable: false,
                sort: null,
            },
            {
                field: "presProfileName",
                label: "_presProfile",
                isSortable: false,
                sort: null,
            },
            {
                field: "procProfileName",
                label: "_procProfile",
                isSortable: false,
                sort: null,
            },
        ];
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
    }
    async _handleDefRowClick(type, e) {
        try {
            const data = e.detail;
            if (!data)
                return;
            const obj = { ...data.value };
            console.log("_handleDefRowClick", obj.rowId);
            const rows = type === "selected" ? [...this.selected] : [...this.unselected];
            if (e.detail.ctrlKey) {
                rows.forEach((row) => {
                    row.selected = row.selected;
                    if (row.pubDefId == obj.pubDefId)
                        row.selected = !row.selected;
                });
                this.pubDefs = rows;
                this._selectedPubDefId = null;
                return;
            }
            rows.forEach((row, idx) => {
                console.log("row", row.rowId, obj.rowId);
                row.selected = idx + 1 === obj.rowId;
            });
            type === "selected" ? (this.selected = rows) : (this.unselected = rows);
        }
        catch (err) {
            console.error("error", err);
        }
        finally {
            // Handle any cleanup or updates
        }
    }
    handleSelect(type, idx) {
        if (type === "selected") {
            const list = JSON.parse(JSON.stringify(this.selected));
            list[idx].active = !list[idx].active;
            this.selected = list;
        }
        else if (type === "deselect") {
            const list = JSON.parse(JSON.stringify(this.unselected));
            list[idx].active = !list[idx].active;
            this.unselected = list;
        }
    }
    handleAction(type) {
        if (type === "select") {
            let a = this.unselected.filter((x) => x.selected);
            a = this.selected.concat(a);
            this.selected = JSON.parse(JSON.stringify(a));
            this.selected.forEach((x) => (x.selected = false));
            this.selected.sort((a, b) => {
                if (a.name.toLowerCase() > b.name.toLowerCase())
                    return 1;
                if (a.name.toLowerCase() < b.name.toLowerCase())
                    return -1;
                return 0;
            });
            let b = this.unselected.filter((x) => !x.selected);
            this.unselected = JSON.parse(JSON.stringify(b));
            this.unselected.forEach((x) => (x.selected = false));
            this.unselected.sort((a, b) => {
                if (a.name.toLowerCase() > b.name.toLowerCase())
                    return 1;
                if (a.name.toLowerCase() < b.name.toLowerCase())
                    return -1;
                return 0;
            });
            this.requestUpdate();
        }
        if (type === "deselect") {
            let a = this.selected.filter((x) => x.selected);
            a = this.unselected.concat(a);
            this.unselected = JSON.parse(JSON.stringify(a));
            this.unselected.forEach((x) => (x.selected = false));
            this.unselected.sort((a, b) => {
                if (a.name.toLowerCase() > b.name.toLowerCase())
                    return 1;
                if (a.name.toLowerCase() < b.name.toLowerCase())
                    return -1;
                return 0;
            });
            let b = this.selected.filter((x) => !x.selected);
            this.selected = JSON.parse(JSON.stringify(b));
            this.selected.forEach((x) => (x.selected = false));
            this.selected.sort((a, b) => {
                if (a.name.toLowerCase() > b.name.toLowerCase())
                    return 1;
                if (a.name.toLowerCase() < b.name.toLowerCase())
                    return -1;
                return 0;
            });
            this.requestUpdate();
        }
        this.dispatchEvent(new CustomEvent("pick-list-updated", {
            detail: {
                selected: this.selected,
                unselected: this.unselected,
            },
            bubbles: true,
            composed: true,
        }));
    }
    renderActions() {
        const template = html `
      <div class="select-actions">
        ${this.actions.map((action) => html `
            <iron-icon
              icon="${action.icon}"
              title="${this.string[action.label]}"
              @click="${this.handleAction.bind(this, action.name)}"
            ></iron-icon>
          `)}
      </div>
    `;
        return template;
    }
    renderTop() {
        const template = html `
      <div class="select-column">
        <span>${this.string["_available"]}</span>
        <wex-table
          .columns=${this.columns}
          .rows="${this.unselected}"
          .enableSelect=${false}
          @click="${this._handleDefRowClick.bind(this, "deselect")}"
        >
        </wex-table>
      </div>
    `;
        return template;
    }
    renderBottom() {
        const template = html `
      <div class="select-column">
        <span>${this.string["_selected"]}</span>
        <wex-table
          .columns=${this.columns}
          .rows="${this.selected}"
          .enableSelect=${false}
          @click="${this._handleDefRowClick.bind(this, "selected")}"
        ></wex-table>
      </div>
    `;
        return template;
    }
    render() {
        return html `
      <wex-col-item justifyContent="stretch">
        ${this.renderTop()} ${this.renderActions()} ${this.renderBottom()}
      </wex-col-item>
    `;
    }
};
__decorate([
    property({ type: Array })
], WexTablePicker.prototype, "selected", void 0);
__decorate([
    property({ type: Array })
], WexTablePicker.prototype, "unselected", void 0);
__decorate([
    state()
], WexTablePicker.prototype, "state", void 0);
__decorate([
    state()
], WexTablePicker.prototype, "string", void 0);
__decorate([
    state()
], WexTablePicker.prototype, "actions", void 0);
WexTablePicker = __decorate([
    customElement("wex-table-picker")
], WexTablePicker);
export { WexTablePicker };
//# sourceMappingURL=table-picker.js.map