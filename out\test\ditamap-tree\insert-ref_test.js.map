{"version": 3, "file": "insert-ref_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/insert-ref_test.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,EAChB,WAAW,GACZ,MAAM,kCAAkC,CAAC;AAE1C,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EACL,gBAAgB,EAEhB,iBAAiB,EACjB,kBAAkB,GACnB,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,QAAQ,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAE5D,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC;YAC9B,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE7C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAChE,+BAA+B,CAChC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC;YAC9B,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACjD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAChE,mCAAmC,CACpC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,IAAI,UAAU,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC;YAC9B,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,aAAa,EAAE,4CAA4C;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC9C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAChE,qDAAqD,CACtD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAE7C,EAAE,CAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC;YAC3B,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,8DAA8D,CAC9D,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1C,qDAAqD,CACtD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC;YAC3B,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,4CAA4C;SAC1D,CAAC,CAAC;QACH,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,aAAa,CACjD,wCAAwC,CACxC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1C,+BAA+B,CAChC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,EAAE,CAAC,IAAI,CAAC,+BAA+B,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAEnD,EAAE,CAAC,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAEtD,EAAE,CAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC", "sourcesContent": ["import {\r\n  MapReferenceNode,\r\n  RootMapNode,\r\n} from \"../../lib/jsDitamap/ditamap-node\";\r\n\r\nimport { expect } from \"@open-wc/testing\";\r\nimport {\r\n  createXmlElement,\r\n  MAP_DITA20,\r\n  MAP_SUBMAP_PHONES,\r\n  PART_SUBMAP_PHONES,\r\n} from \"./data/elements\";\r\nimport * as metadata from \"./files/metadata\";\r\nimport { PHONES_1_BOOKMAP } from \"./files/phones_1_bookmap\";\r\n\r\ndescribe(\"Map Reference Parent\", () => {\r\n  it(\"Inserts a concept as a topicref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let node = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    node.insertNodeFromFile(metadata.BEGIN_FILE);\r\n\r\n    expect(node.children.length).to.equal(1);\r\n\r\n    expect(node.children[0].refElement!.tagName).to.equal(\"topicref\");\r\n    expect(node.children[0].refElement!.getAttribute(\"href\")).to.equal(\r\n      \"/Content/begin_xi1612_1_1.xml\"\r\n    );\r\n  });\r\n\r\n  it(\"Inserts a glossentry as a glossref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let node = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    node.insertNodeFromFile(metadata.DUAL_BAND_FILE);\r\n    expect(node.children.length).to.equal(1);\r\n    expect(node.children[0].refElement!.tagName).to.equal(\"glossref\");\r\n    expect(node.children[0].refElement!.getAttribute(\"href\")).to.equal(\r\n      \"/Content/dual_band_xi1582_1_1.xml\"\r\n    );\r\n  });\r\n\r\n  it(\"Inserts a map as a mapref\", () => {\r\n    let refElement = createXmlElement(PART_SUBMAP_PHONES);\r\n    let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);\r\n    let node = new MapReferenceNode({\r\n      refElement: refElement,\r\n      structuralElement: structuralElement,\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n\r\n    node.insertNodeFromFile(metadata.DITA20_FILE);\r\n    expect(node.children.length).to.equal(1);\r\n    expect(node.children[0].refElement!.tagName).to.equal(\"mapref\");\r\n    expect(node.children[0].refElement!.getAttribute(\"href\")).to.equal(\r\n      \"/Content/dita20_quickstart_guide_xi1683_1_1.ditamap\"\r\n    );\r\n  });\r\n\r\n  it.skip(\"Blocks a self reference\", () => {});\r\n\r\n  it.skip(\"Blocks a bookmap reference\", () => {});\r\n});\r\n\r\ndescribe.only(\"Bookmap Parent\", () => {\r\n  it(\"Inserts a ditamap as a part\", () => {\r\n    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);\r\n    let parent = new RootMapNode({\r\n      rootElement: structuralElement,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    parent.insertNodeFromFile(metadata.DITA20_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      \"[href='/Content/dita20_quickstart_guide_xi1683_1_1.ditamap']\"\r\n    )!;\r\n    expect(target.tagName).to.equal(\"part\");\r\n    expect(target.getAttribute(\"href\")).to.equal(\r\n      \"/Content/dita20_quickstart_guide_xi1683_1_1.ditamap\"\r\n    );\r\n  });\r\n\r\n  it(\"Inserts a topicref as a chapter\", () => {\r\n    let structuralElement = createXmlElement(PHONES_1_BOOKMAP);\r\n    let parent = new RootMapNode({\r\n      rootElement: structuralElement,\r\n      rootMapName: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n    });\r\n    parent.insertNodeFromFile(metadata.BEGIN_FILE);\r\n    let target = parent.structuralElement.querySelector(\r\n      \"[href='/Content/begin_xi1612_1_1.xml']\"\r\n    )!;\r\n    expect(target.tagName).to.equal(\"chapter\");\r\n    expect(target.getAttribute(\"href\")).to.equal(\r\n      \"/Content/begin_xi1612_1_1.xml\"\r\n    );\r\n  });\r\n\r\n  it.skip(\"Blocks a bookmap reference\", () => {});\r\n});\r\n\r\ndescribe(\"Topic Reference Parent\", () => {\r\n  it.skip(\"Inserts a ditamap as a mapref\", () => {});\r\n\r\n  it.skip(\"Inserts a topicref as a topicref\", () => {});\r\n\r\n  it.skip(\"Blocks a bookmap reference\", () => {});\r\n});\r\n"]}