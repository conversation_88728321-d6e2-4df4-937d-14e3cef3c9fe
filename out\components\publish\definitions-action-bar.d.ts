import { LitElement } from "lit-element";
import "@ui5/webcomponents/dist/Button.js";
import "../base/col-item";
import "../base/row-item";
import "../base/icon";
export declare class WexPublishDefinitionsActionBar extends LitElement {
    projects: any[];
    pubDefs: any[];
    _string: any;
    static get styles(): import("lit-element").CSSResult;
    _editPubDef(): Promise<void>;
    _runPubDef(): Promise<void>;
    _deletePubDef(): Promise<void>;
    _handleSelectAllRows: () => void;
    _handleClearSelectedRows: () => void;
    render(): import("lit-element").TemplateResult;
}
