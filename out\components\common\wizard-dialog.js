var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit-element";
import { customElement, property, state } from "lit/decorators.js";
// import { storeInstance } from "store/index.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/FileUploader.js";
import "../wex-table";
import "./list-builder";
// import pubsub from "pubsub-js";
// import { httpError } from "store/httpError.js";
import "@ui5/webcomponents/dist/RadioButton";
import "./wizard-stage-tabs";
import "./dialog";
let WexWizardDialog = class WexWizardDialog extends LitElement {
    constructor() {
        super(...arguments);
        this.open = false;
        this.headerText = "";
        this.width = "auto";
        this.height = "auto";
        this.dialogOpenStateProperty = null;
        this.stages = {};
        this.initialStage = "basic";
        this.mode = "Add";
        this.stage = "";
        this.onConfirm = async () => {
            if (this.stages[this.stage].confirmAction) {
                // TEMP EJS
                const success = await this.stages[this.stage].confirmAction();
                if (!success)
                    return false; // bail out if confirmAction fails
            }
            const nextIdx = Object.keys(this.stages).indexOf(this.stage) + 1;
            this.stage = Object.keys(this.stages)[nextIdx];
            if (!!this.stage) {
                // if there is a stage (next stage) then don't close the dialog
                this.dispatchEvent(new CustomEvent("set-wizard-stage", {
                    detail: this.stage,
                    bubbles: true,
                    composed: true,
                }));
                return false;
            }
            this.stage = "basic";
            return true;
        };
        this.onCancel = async () => {
            // console.log("cancel wizard dialog");
            if (this.stages[this.stage].cancelAction) {
                await this.stages[this.stage].cancelAction();
                this.stage = Object.keys(this.stages)[0]; // set stage to initial
                //   console.log("callback success?", success);
                //   if (!success) return false; // bail out if confirmAction fails
                //   await this.stages[this.stage].confirmAction();
            }
            // not working, but what is correct behavior here? TODO EJS
            // this.stage = "basic";
        };
    }
    static get styles() {
        //   .table-caption-container {
        //     display: flex;
        //     margin-bottom: var(--spacing-sm);
        //     justify-content: space-between;
        //     align-items: center;
        //   }
        // ui5-label {
        //   width: 15%;
        // }
        // .input {
        //   width: 85%;
        // }
        // .input-container {
        //   width: 100%;
        //   display: flex;
        //   align-items: center;
        // }
        // min-width: 60rem;
        return css `
      .dialog-container {
        padding: var(--spacing-lg) var(--spacing-lg);
        transition: height 0.3s ease-out;
        overflow: hidden;
      }

      .body-container {
        height: 100%;
        justify-content: space-between;
        display: flex;
        align-items: flex-start;
        transition:
          transform 0.3s ease-out,
          opacity 0.3s ease-out;
      }

      .tabs-container {
        width: 20%;
      }

      .template-container {
        width: 80%;
        display: block;
        flex: 1;
        align-self: stretch;
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        this.stage = this.initialStage;
    }
    disconnectedCallback() {
        super.disconnectedCallback();
    }
    firstUpdated() { }
    updated(changedProperties) {
        if (changedProperties.has("open")) {
            this.init();
        }
    }
    // stateChange(state: Record<string, any>) {}
    init() { }
    setHeaderText() {
        return this.mode + " " + this.headerText;
    }
    setConfirmLabel() {
        return this.stages[this.stage].confirmLabel || "Confirm";
    }
    setCancelLabel() {
        return this.stages[this.stage].cancelLabel || "Cancel";
    }
    // private setDialogStage() {}
    renderStageTabs() {
        return html `<wex-wizard-stage-tabs
      .stages=${this.stages}
      .stage=${this.stage}
    ></wex-wizard-stage-tabs>`;
    }
    render() {
        return html `
      <wex-dialog
        .open=${this.open}
        .onConfirm=${this.onConfirm}
        .onCancel=${this.onCancel}
        .confirmLabel=${this.setConfirmLabel()}
        .cancelLabel=${this.setCancelLabel()}
        .headerText=${this.setHeaderText()}
        .width=${this.width}
        .height=${this.height}
        .dialogOpenStateProperty=${this.dialogOpenStateProperty}
      >
        <div class="body-container">
          <div class="tabs-container">${this.renderStageTabs()}</div>
          <div class="template-container">
            <slot></slot>
          </div>
        </div>
      </wex-dialog>
    `;
        //   <div class="dialog-container">
        //     </div>
        // ${this.dialogTemplate()}
        //   <div class="footer">${this.footerTemplate()}</div>
    }
};
__decorate([
    property({ type: Boolean })
], WexWizardDialog.prototype, "open", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "headerText", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "width", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "height", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "dialogOpenStateProperty", void 0);
__decorate([
    property({ type: Object })
], WexWizardDialog.prototype, "stages", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "initialStage", void 0);
__decorate([
    property({ type: String })
], WexWizardDialog.prototype, "mode", void 0);
__decorate([
    state()
], WexWizardDialog.prototype, "stage", void 0);
WexWizardDialog = __decorate([
    customElement("wex-wizard-dialog")
], WexWizardDialog);
export { WexWizardDialog };
//# sourceMappingURL=wizard-dialog.js.map