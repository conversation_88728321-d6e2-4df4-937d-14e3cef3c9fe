import { DitamapTree } from "./ditamap-tree";
import { DitamapNode } from "./ditamap-node";
import { FileMeta } from "@bds/types";

export class DitamapCommandManager {
  history: Command[] = [];

  constructor(
    private tree: DitamapTree,
    private setRoot: (root: DitamapNode) => void
  ) {}

  async execute(command: Command) {
    command.execute();
    this.history.push(command);
    await this.tree.rebuildTree();
    this.setRoot(this.tree.getRoot()!);
  }

  undo() {
    this.history.pop()?.undo();
  }
}

export interface Command {
  execute(): void;
  undo(): void;
}

export class InsertCommand implements Command {
  constructor(
    private node: DitamapNode,
    private file: FileMeta
  ) {}

  execute() {
    this.node.insertNodeFromFile(this.file);
  }

  undo() {
    console.log("Undo");
  }
}
