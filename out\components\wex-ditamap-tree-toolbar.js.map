{"version": 3, "file": "wex-ditamap-tree-toolbar.js", "sourceRoot": "", "sources": ["../../src/components/wex-ditamap-tree-toolbar.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,mCAAmC,CAAC;AAG3C,IAAM,qBAAqB,GAA3B,MAAM,qBAAsB,SAAQ,UAAU;IAA9C;;QAEE,iBAAY,GAAY,KAAK,CAAC;IAuDhC,CAAC;IArDC,KAAK,CAAC,KAAa;QACjB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAC1D,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;;;yBAIU,CAAC,IAAI,CAAC,YAAY;qBACtB,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;;;;;;;yBAOtB,CAAC,IAAI,CAAC,YAAY;qBACtB,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;;;yBAOnB,CAAC,IAAI,CAAC,YAAY;qBACtB,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;;;;;;KAMxC,CAAC;IACJ,CAAC;;AAEM,4BAAM,GAAG,GAAG,CAAA;;;;;;;;;;;;;;;GAelB,AAfY,CAeX;AAtDF;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;2DACE;AAF1B,qBAAqB;IAD1B,aAAa,CAAC,0BAA0B,CAAC;GACpC,qBAAqB,CAyD1B", "sourcesContent": ["import { LitElement, css, html } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\n\r\n@customElement(\"wex-ditamap-tree-toolbar\")\r\nclass WexDitamapTreeToolbar extends LitElement {\r\n  @property({ type: Boolean })\r\n  hasSelection: boolean = false;\r\n\r\n  _emit(event: string) {\r\n    this.dispatchEvent(\r\n      new CustomEvent(event, { bubbles: true, composed: true })\r\n    );\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <ul class=\"toolbar\">\r\n        <li>\r\n          <button\r\n            ?disabled=\"${!this.hasSelection}\"\r\n            @click=${() => this._emit(\"insert\")}\r\n          >\r\n            <iron-icon icon=\"vaadin:file-add\"></iron-icon>\r\n          </button>\r\n        </li>\r\n        <li>\r\n          <button\r\n            ?disabled=\"${!this.hasSelection}\"\r\n            @click=${() => this._emit(\"cut\")}\r\n          >\r\n            <iron-icon icon=\"vaadin:scissors\"></iron-icon>\r\n          </button>\r\n        </li>\r\n        <li>\r\n          <button\r\n            ?disabled=\"${!this.hasSelection}\"\r\n            @click=${() => this._emit(\"copy\")}\r\n          >\r\n            <iron-icon icon=\"vaadin:copy-o\"></iron-icon>\r\n          </button>\r\n        </li>\r\n      </ul>\r\n    `;\r\n  }\r\n\r\n  static styles = css`\r\n    .toolbar {\r\n      list-style: none;\r\n      padding: 0;\r\n      display: flex;\r\n      flex-direction: row;\r\n    }\r\n    .toolbar button {\r\n      background: none;\r\n      border: 1px solid;\r\n    }\r\n\r\n    iron-icon {\r\n      --iron-icon-width: 1rem;\r\n    }\r\n  `;\r\n}\r\n"]}