import { FileMeta } from "@bds/types";
export declare const createRefElement: (templateName: keyof typeof templateMap, file: FileMeta) => HTMLElement;
declare const templateMap: {
    mapref: {
        class: string;
        format: string;
        elementTagName: string;
    };
    part: {
        class: string;
        format: string;
        elementTagName: string;
    };
    chapter: {
        class: string;
        format: string;
        elementTagName: string;
    };
    topicref: {
        class: string;
        format: string;
        elementTagName: string;
    };
    glossref: {
        class: string;
        format: string;
        elementTagName: string;
    };
};
export {};
