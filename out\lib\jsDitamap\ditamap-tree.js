import { ContentNode, MapRefLikeElementNode, RootMapNode, } from "./ditamap-node";
export class DitamapTree {
    constructor(rootMapName, files) {
        /**
         * Underlying tree data structure
         */
        this.root = null;
        this.workspace = files ?? new Map();
        this.root = this.buildTree(rootMapName);
        this.rootMapName = rootMapName;
    }
    buildTree(mapName) {
        if (this.workspace.has(mapName)) {
            return this._treeBfs(this.workspace.get(mapName));
        }
        return null;
    }
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml) {
        let tree = new RootMapNode(rootHtml, this.rootMapName);
        let Q = [[rootHtml, tree, this.rootMapName]];
        while (Array.isArray(Q) && Q.length > 0) {
            let [element, parent, mapName] = Q.shift();
            for (let child of element.children) {
                if (DitamapTree.isMapRefLike(child)) {
                    let structuralEl = this.workspace.get(child.getAttribute("href"));
                    if (!structuralEl) {
                        console.warn("No structural element found for", child);
                    }
                    structuralEl = structuralEl ?? child;
                    let newMapRefLikeNode = new MapRefLikeElementNode(child, structuralEl, mapName);
                    let newMapName = DitamapTree.getMapName(child, this.rootMapName);
                    let mapNameToUse = newMapName ?? mapName;
                    if (!newMapName) {
                        console.warn("No map name found for", child);
                    }
                    Q.push([structuralEl, newMapRefLikeNode, mapNameToUse]);
                    parent.children.push(newMapRefLikeNode);
                }
                if (DitamapTree.isContent(child)) {
                    let contentNode = new ContentNode(child, mapName);
                    Q.push([child, contentNode, mapName]);
                    parent.children.push(contentNode);
                }
            }
        }
        return tree;
    }
    //---- Helpers ----
    /**
     * Checks if an html element is a mapref or part
     * @param element
     */
    static isMapRefLike(element) {
        const hasDitamapFormat = element.getAttribute("format") == "ditamap";
        const hasHref = element.getAttribute("href");
        return Boolean(hasDitamapFormat && hasHref);
    }
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isContent(element) {
        return element.tagName === "topicref" || element.tagName === "chapter";
    }
    /**
     * Gets the href used to identify the map, if no href assume it is the root map
     * @param element - xml element
     * @param rootMapName - name of the root map
     */
    static getMapName(element, rootMapName) {
        if (element.getAttribute("href")) {
            return element.getAttribute("href");
        }
        if (rootMapName &&
            (element.tagName.toLowerCase() === "bookmap" ||
                element.tagName.toLowerCase() === "map")) {
            return rootMapName;
        }
        return undefined;
    }
    static getMapTitle(element) {
        let mainbooktitleEl = element.querySelector("mainbooktitle");
        if (mainbooktitleEl)
            return mainbooktitleEl.innerHTML;
        let titleEl = element.querySelector("title");
        if (titleEl)
            return titleEl.innerText || titleEl.innerHTML;
        let titleAtt = element.getAttribute("title");
        if (titleAtt)
            return titleAtt;
        return element.tagName;
    }
    static removeXmlNoise(str) {
        if (str)
            return str.replace("<?xm-replace_text Main Book Title ?>", "");
        return str;
    }
}
// const structuralElements = new Set([
//   "map",
//   "bookmap",
//   "topichead",
//   "topicgroup",
// ]);
// const referenceElements = new Set(["mapref", "part", "keydef"]);
// const dualUseElements = new Set(["topicref", "chapter", "appendix"]);
/**
 *
 * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
//# sourceMappingURL=ditamap-tree.js.map