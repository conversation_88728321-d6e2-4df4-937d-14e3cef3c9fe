import { ContentNode, MapRefLikeElementNode, RootMapNode, } from "./ditamap-node";
import { DitamapUtils } from "./ditamap-utils";
export class DitamapTree {
    constructor(rootMapName, files) {
        /**
         * Underlying tree data structure
         */
        this.root = null;
        this.workspace = files ?? new Map();
        this.root = this.buildTree(rootMapName);
        this.rootMapName = rootMapName;
    }
    buildTree(mapName) {
        if (this.workspace.has(mapName)) {
            return this._treeBfs(this.workspace.get(mapName));
        }
        return null;
    }
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml) {
        let tree = new RootMapNode(rootHtml, this.rootMapName);
        let Q = [[rootHtml, tree, this.rootMapName]];
        while (Array.isArray(Q) && Q.length > 0) {
            let [element, parent, mapName] = Q.shift();
            for (let child of element.children) {
                if (DitamapUtils.isMapRefLike(child)) {
                    let structuralEl = this.workspace.get(child.getAttribute("href"));
                    if (!structuralEl) {
                        console.warn("No structural element found for", child);
                    }
                    structuralEl = structuralEl ?? child;
                    let newMapRefLikeNode = new MapRefLikeElementNode(child, structuralEl, mapName);
                    let newMapName = DitamapUtils.getMapName(child, this.rootMapName);
                    let mapNameToUse = newMapName ?? mapName;
                    if (!newMapName) {
                        console.warn("No map name found for", child);
                    }
                    Q.push([structuralEl, newMapRefLikeNode, mapNameToUse]);
                    parent.children.push(newMapRefLikeNode);
                }
                else if (DitamapUtils.isTopicRefLike(child)) {
                    let contentNode = new ContentNode(child, mapName);
                    Q.push([child, contentNode, mapName]);
                    parent.children.push(contentNode);
                }
            }
        }
        return tree;
    }
}
// const structuralElements = new Set([
//   "map",
//   "bookmap",
//   "topichead",
//   "topicgroup",
// ]);
// const referenceElements = new Set(["mapref", "part", "keydef"]);
// const dualUseElements = new Set(["topicref", "chapter", "appendix"]);
/**
 *
 * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
//# sourceMappingURL=ditamap-tree.js.map