{"version": 3, "file": "list-picker.js", "sourceRoot": "", "sources": ["../../../src/components/common/list-picker.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,oBAAoB,CAAC;AAE5B,mEAAmE;AACnE,iEAAiE;AACjE,qDAAqD;AAG9C,IAAM,aAAa,GAAnB,MAAM,aAAc,SAAQ,UAAU;IAAtC;;QACsB,aAAQ,GAAe,EAAE,CAAC;QAC1B,eAAU,GAAe,EAAE,CAAC;QAC9C,WAAM,GAAQ,EAAE,CAAC;QACjB,YAAO,GAAQ,EAAE,CAAC;QAClB,aAAQ,GAAe,EAAE,CAAC;IA8RrC,CAAC;IA3RC,MAAM,KAAK,MAAM;QACf,MAAM;QACN,4BAA4B;QAC5B,IAAI;QACJ,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+FT,CAAC;IACJ,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YAC1D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAEO,IAAI;QACV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC;IAC1D,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED,qCAAqC;IAC7B,YAAY,CAAC,IAAY,EAAE,GAAW;QAC5C,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,aAAa;YACb,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,aAAa;YACb,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,KAAK,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACrB,CAAC;QAED,gDAAgD;QAChD,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,mBAAmB,EAAE;YACnC,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;YACD,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAES,MAAM;QACd,OAAO,IAAI,CAAA;;;kBAGG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;;cAE9B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;YACvB,CAAC,CAAC,IAAI,CAAA;;;yBAGK,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;;;iBAG1C;YACH,CAAC,CAAC,IAAI;cACN,IAAI,CAAC,UAAU,CAAC,GAAG,CACnB,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CACZ,IAAI,CAAA;6BACS,IAAI,CAAC,MAAM;4BACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC;;0BAE/C,IAAI,CAAC,IAAI;sBACb,CACT;;;;;YAKD,IAAI,CAAC,QAAQ,CAAC,GAAG,CACjB,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAA;;wBAEC,CAAC,CAAC,IAAI;yBACL,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;0BACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;;aAEjD,CACF;;;;kBAIO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;;cAE7B,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;YACrB,CAAC,CAAC,IAAI,CAAA;;;yBAGK,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;;;iBAGzC;YACH,CAAC,CAAC,IAAI;cACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CACjB,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CACZ,IAAI,CAAA;6BACS,IAAI,CAAC,MAAM;4BACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC;;0BAE/C,IAAI,CAAC,IAAI;sBACb,CACT;;;;KAIR,CAAC;IACJ,CAAC;CACF,CAAA;AAlS4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;+CAA2B;AAC1B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iDAA6B;AAC9C;IAAR,KAAK,EAAE;6CAAkB;AACjB;IAAR,KAAK,EAAE;8CAAmB;AAClB;IAAR,KAAK,EAAE;+CAA2B;AALxB,aAAa;IADzB,aAAa,CAAC,iBAAiB,CAAC;GACpB,aAAa,CAmSzB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\nimport \"../base/fixed-item\";\r\n\r\n// objects in the arrays must have a name key to function correctly\r\n// the primary use case is currently to update values on a pubdef\r\n// emits both selected and unselected on every change\r\n\r\n@customElement(\"wex-list-picker\")\r\nexport class WexListPicker extends LitElement {\r\n  @property({ type: Array }) selected: Array<any> = [];\r\n  @property({ type: Array }) unselected: Array<any> = [];\r\n  @state() _state: any = {};\r\n  @state() _string: any = {};\r\n  @state() _actions: Array<any> = [];\r\n  private _subscription: any;\r\n\r\n  static get styles() {\r\n    // * {\r\n    //   box-sizing: border-box;\r\n    // }\r\n    return css`\r\n      :host {\r\n        flex: 1;\r\n      }\r\n      .italic {\r\n        font-style: italic;\r\n      }\r\n\r\n      #dialog {\r\n        width: min(90vw, 600px);\r\n      }\r\n      .dialog-content {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        padding: 1rem;\r\n        height: min(50vh, 300px);\r\n        color: var(--font-color);\r\n      }\r\n      .dialog-content * {\r\n        color: var(--font-color);\r\n      }\r\n      .dialog-content > *:not(:last-child) {\r\n        margin-right: 0.5rem;\r\n      }\r\n\r\n      ul {\r\n        flex-grow: 1;\r\n        margin: 0;\r\n        padding: 0;\r\n        list-style: none;\r\n        border: 1px solid var(--clr-gray-light);\r\n        overflow-y: auto;\r\n        max-height: calc(2.25rem * 8);\r\n        height: 30vh;\r\n      }\r\n      li {\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 1rem;\r\n        height: 2.25rem;\r\n        border-bottom: 1px solid var(--clr-gray-light);\r\n      }\r\n      li > span {\r\n        flex-grow: 1;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n      li > .icon-container {\r\n        position: relative;\r\n        width: 1.5rem;\r\n        height: 1.5rem;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 8px;\r\n      }\r\n      li > .icon-container:hover {\r\n        background: var(--clr-white);\r\n      }\r\n      li > *:not(:last-child) {\r\n        margin-right: 1rem;\r\n      }\r\n      li[active] {\r\n        background: var(--row-selected-background);\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n      li:not([active]):hover {\r\n        background: var(--clr-gray-ultra-light);\r\n      }\r\n\r\n      .select-column {\r\n        flex-grow: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n        width: 100%;\r\n      }\r\n      .select-column > span {\r\n        margin-bottom: 0.5rem;\r\n      }\r\n\r\n      .select-actions {\r\n        display: flex;\r\n        flex-direction: column;\r\n      }\r\n      .select-actions > *:not(:last-child) {\r\n        margin-bottom: 0.5rem;\r\n      }\r\n      .select-actions > * {\r\n        cursor: pointer;\r\n      }\r\n    `;\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    this._state = storeInstance.state;\r\n    this._string = this._state[this._state.langCode];\r\n    this._subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this.init();\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  private init() {\r\n    this._actions = this._state.menus.dialog_select_actions;\r\n  }\r\n\r\n  private stateChange(state: any) {\r\n    this._state = state;\r\n    this._string = state[state.langCode];\r\n  }\r\n\r\n  // idx - index from render function i\r\n  private handleSelect(type: string, idx: number) {\r\n    if (type === \"selected\") {\r\n      // deep clone\r\n      const list = JSON.parse(JSON.stringify(this.selected));\r\n      list[idx].active = !list[idx].active;\r\n      this.selected = list;\r\n    } else if (type === \"deselect\") {\r\n      // deep clone\r\n      const list = JSON.parse(JSON.stringify(this.unselected));\r\n      list[idx].active = !list[idx].active;\r\n      this.unselected = list;\r\n    }\r\n  }\r\n\r\n  private handleAction(type: string) {\r\n    if (type === \"select\") {\r\n      let a = this.unselected.filter((x) => x.active);\r\n      a = this.selected.concat(a);\r\n      this.selected = JSON.parse(JSON.stringify(a));\r\n      this.selected.forEach((x) => (x.active = false));\r\n      this.selected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      let b = this.unselected.filter((x) => !x.active);\r\n      this.unselected = JSON.parse(JSON.stringify(b));\r\n      this.unselected.forEach((x) => (x.active = false));\r\n      this.unselected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      this.requestUpdate();\r\n    } else if (type === \"deselect\") {\r\n      let a = this.selected.filter((x) => x.active);\r\n      a = this.unselected.concat(a);\r\n      this.unselected = JSON.parse(JSON.stringify(a));\r\n      this.unselected.forEach((x) => (x.active = false));\r\n      this.unselected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      let b = this.selected.filter((x) => !x.active);\r\n      this.selected = JSON.parse(JSON.stringify(b));\r\n      this.selected.forEach((x) => (x.active = false));\r\n      this.selected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      this.requestUpdate();\r\n    } else if (type === \"select-all\") {\r\n      this.selected = this.selected.concat(this.unselected);\r\n      this.selected.forEach((x) => (x.active = false));\r\n      this.selected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n      this.unselected = [];\r\n    } else if (type === \"deselect-all\") {\r\n      this.unselected = this.unselected.concat(this.selected);\r\n      this.unselected.forEach((x) => (x.active = false));\r\n      this.unselected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n      this.selected = [];\r\n    }\r\n\r\n    // needs to event for every change at the moment\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"pick-list-updated\", {\r\n        detail: {\r\n          selected: this.selected,\r\n          unselected: this.unselected,\r\n        },\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n  }\r\n\r\n  protected render() {\r\n    return html`\r\n      <wex-row-item justifyContent=\"stretch\">\r\n        <wex-col-item>\r\n          <span>${this._string[\"_available\"]}</span>\r\n          <ul>\r\n            ${!this.unselected.length\r\n              ? html`\r\n                  <li>\r\n                    <span class=\"italic\"\r\n                      >${this._string[\"_no_item_available\"]}</span\r\n                    >\r\n                  </li>\r\n                `\r\n              : null}\r\n            ${this.unselected.map(\r\n              (item, idx) =>\r\n                html`<li\r\n                  ?active=\"${item.active}\"\r\n                  @click=\"${this.handleSelect.bind(this, \"deselect\", idx)}\"\r\n                >\r\n                  <span>${item.name}</span>\r\n                </li>`\r\n            )}\r\n          </ul>\r\n        </wex-col-item>\r\n\r\n        <wex-fixed-item justifyContent=\"center\" alignItems=\"center\">\r\n          ${this._actions.map(\r\n            (x) => html`\r\n              <iron-icon\r\n                icon=\"${x.icon}\"\r\n                title=\"${this._string[x.label]}\"\r\n                @click=\"${this.handleAction.bind(this, x.name)}\"\r\n              ></iron-icon>\r\n            `\r\n          )}\r\n        </wex-fixed-item>\r\n\r\n        <wex-col-item>\r\n          <span>${this._string[\"_selected\"]}</span>\r\n          <ul>\r\n            ${!this.selected.length\r\n              ? html`\r\n                  <li>\r\n                    <span class=\"italic\"\r\n                      >${this._string[\"_no_item_selected\"]}</span\r\n                    >\r\n                  </li>\r\n                `\r\n              : null}\r\n            ${this.selected.map(\r\n              (item, idx) =>\r\n                html`<li\r\n                  ?active=\"${item.active}\"\r\n                  @click=\"${this.handleSelect.bind(this, \"selected\", idx)}\"\r\n                >\r\n                  <span>${item.name}</span>\r\n                </li>`\r\n            )}\r\n          </ul>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n}\r\n"]}