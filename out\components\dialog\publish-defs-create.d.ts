import { LitElement } from "lit";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import "../base/col-item";
import "../common/wizard-dialog";
import "../common/list-picker";
import "../common/table-picker";
interface DialogOpenData {
    mode?: string;
    pubDef?: any;
    projects?: any[];
}
interface PubContent {
    pubContentId: number;
    name: string;
    [key: string]: any;
}
interface PubLang {
    langId: number;
    description: string;
    name?: string;
}
interface PubOutput {
    outputTypeId: number;
    name: string;
}
interface PubDef {
    name: string;
    desc: string;
    projectId: number;
    pubDefId: number;
    lstPubContentDtos: PubContent[];
    lstLanguages: PubLang[];
    lstOutputFormats: PubOutput[];
}
interface Project {
    name: string;
    projectId: number;
}
export declare class WexDialogPublishDefsCreate extends LitElement {
    dialogOpenData: DialogOpenData | null;
    state: Record<string, any>;
    string: Record<string, any>;
    stage: string;
    pubContent: PubContent[];
    pubLangs: PubLang[];
    pubOutputs: PubOutput[];
    selectedPubContent: PubContent[];
    selectedPubLangs: PubLang[];
    selectedPubOutputs: PubOutput[];
    pubDef: PubDef | null;
    pubDefId: number;
    selectedProject: Project | null;
    selectedProjectId: number;
    selectedProjectName: string;
    selectedProjectDesc: string;
    _name: string;
    _desc: string;
    private subscription;
    private pubDefNames;
    private initialStage;
    private readonly stages;
    private mode;
    private _errors;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    protected updated(changedProperties: Map<string | number | symbol, unknown>): void;
    private stateChange;
    private setMode;
    private setBasicInfo;
    private setPubContent;
    private setPubLangs;
    private setPubOutputs;
    private init;
    private handleProjectSelect;
    private handleChange;
    private isValidString;
    private validate;
    private confirm;
    private handlePickListUpdated;
    private renderBasicInfoStage;
    private renderTablePicker;
    private renderListPicker;
    private renderContentStage;
    private renderLanguagesStage;
    private renderOutputsStage;
    private log;
    private renderFilenameStage;
    private renderBody;
    protected render(): import("lit").TemplateResult<1>;
}
export {};
