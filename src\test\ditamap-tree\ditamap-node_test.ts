import { RootMapNode } from "../../lib/jsDitamap/ditamap-node";
import { expect } from "@open-wc/testing";

describe("Root Map Node", () => {
  it("should create node from bookmap", () => {
    let element = new DOMParser().parseFromString(
      `
          <bookmap 
          class="- map/map bookmap/bookmap ">
              <booktitle class="- topic/title bookmap/booktitle ">
          <mainbooktitle class="- topic/ph bookmap/mainbooktitle ">
              <?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>
      </booktitle>
          </bookmap>
            `,
      "application/xml"
    ).documentElement;
    let node = new RootMapNode({
      rootElement: element,
      rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
    });
    expect(node).to.deep.equal({
      href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      containingMap: null,
      title: "Phone 1 User Guide",
      refElement: null,
      structuralElement: element,
      mapPath: [],
      children: [],
    });
  });
});
