var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// @ts-ignore
import * as wexlib from "../../lib/wexlib.js";
import "../base/col-item";
import "../common/dialog";
import "../common/select";
import "../base/row-item";
let WexDialogBrowsePublish = class WexDialogBrowsePublish extends LitElement {
    constructor() {
        super(...arguments);
        this.dialogOpenData = null;
        this.state = {};
        this.string = {};
        this.presentationProfile = null;
        this.processingProfile = null;
        this.outputFormat = null; // falsy value to disable submit button
        this.subscription = null;
        this.presentationProfiles = [];
        this.processingProfiles = [];
        this.outputFormats = [];
        this.outputExt = "";
        this.outputFileName = "";
        this.selectedFile = null;
        this.publish = () => {
            console.log("publish");
            console.log("publication", this.selectedFile);
            console.log("outputFormat", this.outputFormat);
            console.log("presentationProfile", this.presentationProfile);
            console.log("processingProfile", this.processingProfile);
            console.log("outputFileName", this.outputFileName);
            try {
                const publicationContentId = -1;
                const resLblIdMap = this.selectedFile?.lineageId;
                const procProfileLblId = this.processingProfile?.profileLblId;
                const presProfileLblId = this.presentationProfile?.profileLblId;
                const publishEngineId = this.outputFormat?.publishEngineId;
                const outputTypeId = this.outputFormat?.outputTypeId;
                const outputFileName = this.outputFileName;
                const params = {
                    publicationContentId: `${publicationContentId}`,
                    procProfileLblId: `${procProfileLblId ?? ""}`,
                    presProfileLblId: `${presProfileLblId ?? ""}`,
                    publishEngineId: `${publishEngineId ?? ""}`,
                    outputFileName: `${outputFileName ?? ""}`,
                    outputTypeId: `${outputTypeId ?? ""}`,
                    resLblIdMap: `${resLblIdMap ?? ""}`,
                };
                const body = new URLSearchParams(params);
                //   console.log("body", body);
                const res = fetch("/lwa/jrest/RunAdHocPublish", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    body,
                });
                return true;
            }
            catch (e) {
                console.error(e);
            }
            finally {
                this.outputFileName = "";
            }
        };
    }
    connectedCallback() {
        super.connectedCallback();
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.setPresentationProfiles();
        this.setProcessingProfiles();
        this.setOutputFormats();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    updated(changedProperties) {
        const dialog = this.shadowRoot?.querySelector("ui5-dialog");
        if (dialog &&
            changedProperties.has("dialogOpenData") &&
            this.dialogOpenData) {
            dialog.open = true;
        }
    }
    async setPresentationProfiles() {
        const res = await wexlib.getPresentationProfiles();
        this.presentationProfiles = res;
        this.presentationProfile = this.presentationProfiles[0]; // also set the current profile
    }
    async setProcessingProfiles() {
        const res = await wexlib.getProcessingProfiles();
        this.processingProfiles = res;
        this.processingProfile = this.processingProfiles[0]; // set default here now because it's defined
    }
    async setOutputFormats() {
        const res = await wexlib.getPubEngineOutputs();
        this.outputFormats = res;
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        this.dialogOpenData = state.browse_publish_dialog_open;
        this.selectedFile = state.browse_selected_file;
    }
    handleInputChange(e) {
        const target = e.target;
        this.outputFileName = target.value; // update outputFileName with input value
    }
    renderBody() {
        // <h3>${this._string["_project_name"]}</h3>
        return html `
      <wex-row-item>
        <wex-col-item>
          <h3>Map</h3>
          <div class="more-item">
            <span title="Map">${this.selectedFile?.name}</span>
          </div>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Output File Name</h3>
          <ui5-input
            value="${this.outputFileName}"
            @input="${(e) => this.handleInputChange(e)}"
          ></ui5-input>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Output Format</h3>
          <wex-select
            .options="${this.outputFormats}"
            .selectedOption="${this.outputFormat}"
            @selection-changed="${(e) => this.handleSelectionChange(e.detail.selectedOption, "outputFormat")}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Presentation Profile</h3>
          <wex-select
            .showPlaceholder="${false}"
            .options="${this.presentationProfiles}"
            .selectedOption="${this.presentationProfile}"
            @selection-changed="${(e) => this.handleSelectionChange(e.detail.selectedOption, "presentationProfile")}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Processing Profile</h3>
          <wex-select
            .showPlaceholder="${false}"
            .options="${this.processingProfiles}"
            .selectedOption="${this.processingProfile}"
            @selection-changed="${(e) => this.handleSelectionChange(e.detail.selectedOption, "processingProfile")}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>
    `;
    }
    render() {
        // <ui5-dialog header-text="${this.string["_browse_publish_dialog"]}">
        return html `
      <wex-dialog
        headerText="Run Adhoc Publishing Job"
        dialogOpenStateProperty=${"browse_publish_dialog_open"}
        .confirmDisabled="${!this.outputFormat}"
        .open="${!!this.dialogOpenData}"
        .onConfirm="${this.publish}"
        confirmLabel="Publish"
        cancelLabel="Cancel"
        width="40%"
      >
        ${this.renderBody()}
      </wex-dialog>
    `;
    }
    handleSelectionChange(selectedOption, property) {
        // this check is broken and seems useless
        // if (!Object.hasOwnProperty.call(this, property)) {
        //   throw new Error(
        //     `Property "${property}" does not exist on this component.`
        //   );
        // }
        this[property] = selectedOption;
        // If outputFormat was changed, update filename parts
        if (property === "outputFormat") {
            console.log("handleSelectionChange: outputFormat", selectedOption);
            this.outputExt = selectedOption?.outputExt || "";
            this.outputFileName =
                selectedOption && this.selectedFile
                    ? this.selectedFile.name.split(".")[0] + this.outputExt
                    : "";
        }
        this.requestUpdate();
        console.log("handleSelectionChange: this.outputFormat", this.outputFormat);
    }
};
WexDialogBrowsePublish.styles = css `
    ui5-input {
      width: 100%;
    }
    .more-item {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 1rem;
      height: 2.25rem;
      border: 1px solid var(--clr-gray-light);
    }
  `;
__decorate([
    property({ type: Object })
], WexDialogBrowsePublish.prototype, "dialogOpenData", void 0);
__decorate([
    property({ type: Object })
], WexDialogBrowsePublish.prototype, "state", void 0);
__decorate([
    property({ type: Object })
], WexDialogBrowsePublish.prototype, "string", void 0);
__decorate([
    state()
], WexDialogBrowsePublish.prototype, "presentationProfile", void 0);
__decorate([
    state()
], WexDialogBrowsePublish.prototype, "processingProfile", void 0);
__decorate([
    state()
], WexDialogBrowsePublish.prototype, "outputFormat", void 0);
WexDialogBrowsePublish = __decorate([
    customElement("wex-dialog-browse-publish")
], WexDialogBrowsePublish);
export { WexDialogBrowsePublish };
//# sourceMappingURL=browse-publish.js.map