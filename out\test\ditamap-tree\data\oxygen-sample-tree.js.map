{"version": 3, "file": "oxygen-sample-tree.js", "sourceRoot": "", "sources": ["../../../../src/test/ditamap-tree/data/oxygen-sample-tree.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,kBAAkB,GAAyB;IACtD,KAAK,EAAE,eAAe;IACtB,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,mBAAmB;IACzB,eAAe,EAAE,SAAS;IAC1B,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE;QACR;YACE,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,oBAAoB;YAC1B,aAAa,EAAE,mBAAmB;YAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;YAC9B,eAAe,EAAE,KAAK;YACtB,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,uBAAuB;oBAC7B,aAAa,EAAE,oBAAoB;oBACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;oBACpD,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,qBAAqB;oBAC5B,IAAI,EAAE,0BAA0B;oBAChC,aAAa,EAAE,oBAAoB;oBACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;oBACpD,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,wBAAwB;oBAC9B,aAAa,EAAE,oBAAoB;oBACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;oBACpD,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,oBAAoB;oBAC1B,aAAa,EAAE,oBAAoB;oBACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;oBACpD,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,UAAU;4BAChB,KAAK,EAAE,gBAAgB;4BACvB,IAAI,EAAE,oBAAoB;4BAC1B,aAAa,EAAE,oBAAoB;4BACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;4BACpD,eAAe,EAAE,UAAU;4BAC3B,QAAQ,EAAE,EAAE;yBACb;wBACD;4BACE,IAAI,EAAE,UAAU;4BAChB,KAAK,EAAE,gBAAgB;4BACvB,IAAI,EAAE,oBAAoB;4BAC1B,aAAa,EAAE,oBAAoB;4BACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;4BACpD,eAAe,EAAE,UAAU;4BAC3B,QAAQ,EAAE,EAAE;yBACb;wBACD;4BACE,IAAI,EAAE,UAAU;4BAChB,KAAK,EAAE,gBAAgB;4BACvB,IAAI,EAAE,oBAAoB;4BAC1B,aAAa,EAAE,oBAAoB;4BACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;4BACpD,eAAe,EAAE,UAAU;4BAC3B,QAAQ,EAAE,EAAE;yBACb;qBACF;iBACF;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,qBAAqB;oBAC5B,IAAI,EAAE,0BAA0B;oBAChC,aAAa,EAAE,oBAAoB;oBACnC,OAAO,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;oBACpD,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;aACF;SACF;QACD;YACE,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,aAAa;YACpB,IAAI,EAAE,kBAAkB;YACxB,aAAa,EAAE,mBAAmB;YAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;YAC9B,eAAe,EAAE,SAAS;YAC1B,QAAQ,EAAE,EAAE;SACb;QACD;YACE,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,aAAa;YACpB,IAAI,EAAE,kBAAkB;YACxB,aAAa,EAAE,mBAAmB;YAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;YAC9B,eAAe,EAAE,SAAS;YAC1B,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,sBAAsB;oBAC5B,aAAa,EAAE,mBAAmB;oBAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;oBAC9B,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,iBAAiB;oBACxB,IAAI,EAAE,qBAAqB;oBAC3B,aAAa,EAAE,mBAAmB;oBAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;oBAC9B,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,uBAAuB;oBAC7B,aAAa,EAAE,mBAAmB;oBAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;oBAC9B,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,EAAE;iBACb;aACF;SACF;QACD;YACE,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,eAAe;YACtB,IAAI,EAAE,oBAAoB;YAC1B,aAAa,EAAE,mBAAmB;YAClC,OAAO,EAAE,CAAC,mBAAmB,CAAC;YAC9B,eAAe,EAAE,UAAU;YAC3B,QAAQ,EAAE,EAAE;SACb;KACF;CACF,CAAC", "sourcesContent": ["export const OXYGE<PERSON>_SAMPLE_TREE: DitamapNodeStructure = {\r\n  title: \"Product tasks\",\r\n  type: \"map\",\r\n  href: \"/taskbook.ditamap\",\r\n  rootElementName: \"bookmap\",\r\n  mapPath: [],\r\n  children: [\r\n    {\r\n      type: \"map\",\r\n      title: \"Installing\",\r\n      href: \"installing.ditamap\",\r\n      containingMap: \"/taskbook.ditamap\",\r\n      mapPath: [\"/taskbook.ditamap\"],\r\n      rootElementName: \"map\",\r\n      children: [\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Install Overview\",\r\n          href: \"install_overview.dita\",\r\n          containingMap: \"installing.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"System Requirements\",\r\n          href: \"system_requirements.dita\",\r\n          containingMap: \"installing.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Download Software\",\r\n          href: \"download_software.dita\",\r\n          containingMap: \"installing.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Install Steps\",\r\n          href: \"install_steps.dita\",\r\n          containingMap: \"installing.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [\r\n            {\r\n              type: \"topicref\",\r\n              title: \"Install Step 1\",\r\n              href: \"install_step1.dita\",\r\n              containingMap: \"installing.ditamap\",\r\n              mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n              rootElementName: \"topicref\",\r\n              children: [],\r\n            },\r\n            {\r\n              type: \"topicref\",\r\n              title: \"Install Step 2\",\r\n              href: \"install_step2.dita\",\r\n              containingMap: \"installing.ditamap\",\r\n              mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n              rootElementName: \"topicref\",\r\n              children: [],\r\n            },\r\n            {\r\n              type: \"topicref\",\r\n              title: \"Install Step 3\",\r\n              href: \"install_step3.dita\",\r\n              containingMap: \"installing.ditamap\",\r\n              mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n              rootElementName: \"topicref\",\r\n              children: [],\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Verify Installation\",\r\n          href: \"verify_installation.dita\",\r\n          containingMap: \"installing.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\", \"installing.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      type: \"chapter\",\r\n      title: \"Configuring\",\r\n      href: \"configuring.dita\",\r\n      containingMap: \"/taskbook.ditamap\",\r\n      mapPath: [\"/taskbook.ditamap\"],\r\n      rootElementName: \"chapter\",\r\n      children: [],\r\n    },\r\n    {\r\n      type: \"chapter\",\r\n      title: \"Maintaining\",\r\n      href: \"maintaining.dita\",\r\n      containingMap: \"/taskbook.ditamap\",\r\n      mapPath: [\"/taskbook.ditamap\"],\r\n      rootElementName: \"chapter\",\r\n      children: [\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Maintain Storage\",\r\n          href: \"maintainstorage.dita\",\r\n          containingMap: \"/taskbook.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Maintain Server\",\r\n          href: \"maintainserver.dita\",\r\n          containingMap: \"/taskbook.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n        {\r\n          type: \"topicref\",\r\n          title: \"Maintain Database\",\r\n          href: \"maintaindatabase.dita\",\r\n          containingMap: \"/taskbook.ditamap\",\r\n          mapPath: [\"/taskbook.ditamap\"],\r\n          rootElementName: \"topicref\",\r\n          children: [],\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      type: \"appendix\",\r\n      title: \"Task Appendix\",\r\n      href: \"task_appendix.dita\",\r\n      containingMap: \"/taskbook.ditamap\",\r\n      mapPath: [\"/taskbook.ditamap\"],\r\n      rootElementName: \"appendix\",\r\n      children: [],\r\n    },\r\n  ],\r\n};"]}