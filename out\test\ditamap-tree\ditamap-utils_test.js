import { DitamapUtils } from "../../lib/jsDitamap/ditamap-utils";
import { expect } from "@open-wc/testing";
describe("Is Map reference", () => {
    it("part with href and format=ditamap => true", () => {
        let document = new DOMParser().parseFromString(`<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
          class="- map/topicref bookmap/part " />`, "application/xml");
        expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;
    });
    it("mapref with href and format=ditamap => true", () => {
        let document = new DOMParser().parseFromString(`<mapref format="ditamap" href="/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap"
          class="+ map/topicref mapgroup-d/mapref " />`, "application/xml");
        expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;
    });
});
describe("Is TopicRefLike", () => {
    it("topicref => true", () => {
        let document = new DOMParser().parseFromString(`<topicref href="/Content/begin_xi1612_1_1.xml" class="- map/topicref " />`, "application/xml");
        expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
    });
    it("chapter => true", () => {
        let document = new DOMParser().parseFromString(`<chapter href="/Content/Introduction_xi1674_1_1.xml" class="- map/topicref bookmap/chapter">`, "application/xml");
        expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
    });
    it("topichead => true", () => {
        let document = new DOMParser().parseFromString(`<topichead href="/Content/Introduction_xi1674_1_1.xml" class="- map/topicref bookmap/chapter">`, "application/xml");
        expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
    });
    it("bookmap => false", () => {
        let document = new DOMParser().parseFromString(`<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
          id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
          class="- map/map bookmap/bookmap ">`, "application/xml");
        expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;
    });
    it("part => false", () => {
        let document = new DOMParser().parseFromString(`<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
          class="- map/topicref bookmap/part " />`, "application/xml");
        expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;
    });
});
describe("Get map name (href)", () => {
    it("If the element is a bookmap, use the rootMapName", () => {
        let document = new DOMParser().parseFromString(`<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
          id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
          class="- map/map bookmap/bookmap ">`, "application/xml");
        expect(DitamapUtils.getMapName(document.documentElement, "/Content/Phone1_Bookmap_xi1577_1_1.ditamap")).to.equal("/Content/Phone1_Bookmap_xi1577_1_1.ditamap");
    });
});
//# sourceMappingURL=ditamap-utils_test.js.map