import { DitamapTree } from "../../lib/jsDitamap/ditamap-tree";
import { PHONES_TREE } from "./data/json-trees";
import { PHONES_1_BOOKMAP, SUBMAP_PHONES } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";
describe("Building JSON tree from xml", () => {
    it("builds phones tree", () => {
        let bookmap = new DOMParser().parseFromString(PHONES_1_BOOKMAP, "application/xml").documentElement;
        let submap = new DOMParser().parseFromString(SUBMAP_PHONES, "application/xml").documentElement;
        let workspace = new Map();
        workspace.set("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", bookmap);
        workspace.set("/Content/submap_phones_xi1609_1_1.ditamap", submap);
        let tree = new DitamapTree("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", workspace);
        expect(pruneTree(PHONES_TREE, ["href", "children"])).to.deep.equal(pruneTree(tree.root, [
            "href",
            "children",
            // "containingMap",
        ]));
    });
});
const pruneTree = (current, fields) => {
    if (!current)
        return null;
    let pruned = {};
    fields.forEach((field) => {
        if (current[field] != undefined)
            pruned[field] = current[field];
    });
    pruned.children = current.children.map((child) => pruneTree(child, fields));
    return pruned;
};
//# sourceMappingURL=ditamap-tree_test.js.map