{"version": 3, "file": "ditamap-utils.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-utils.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,YAAY;IACvB;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,OAAoB;QACtC,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC;QACrE,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc,CAAC,OAAoB;QACxC,IACE,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,MAAM;YACxC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,QAAQ;YAC1C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,SAAS;YAC3C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK;YAEvC,OAAO,KAAK,CAAC;QAEf,gDAAgD;QAChD,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAC7B,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,eAAe,GAAG,IAAI,CAAC;QAE3B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACzD,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QACpE,CAAC;QAED,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC;QAC3E,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,QAAQ,CAAC;QAEtD,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QAE/C,IAAI,OAAO,CAAC,QAAQ,IAAI,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI,SAAS,EAAE,CAAC;YACpE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,aAAa,GAAG,IAAI,CAAC;YACvB,CAAC;QACH,CAAC;QAED,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC;QAE/D,IACE,CAAC,WAAW;YACV,OAAO;YACP,gBAAgB;YAChB,eAAe;YACf,SAAS,CAAC;YACZ,YAAY;YACZ,aAAa,EACb,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,UAAU,CACf,OAAoB,EACpB,WAAoB;QAEpB,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAW,CAAC;QAChD,CAAC;QAED,IACE,WAAW;YACX,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,SAAS;gBAC1C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAC1C,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAoB;QACrC,OAAO,YAAY,CAAC,cAAc,CAChC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CACpC,CAAC,IAAI,EAAE,CAAC;IACX,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAoB;QACvC,IAAI,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC7D,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC,SAAS,CAAC;QAEtD,IAAI,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;QAE3D,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAE9B,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,IAAI,GAAG;YACL,OAAO,GAAG,CAAC,OAAO,CAAC,6CAA6C,EAAE,EAAE,CAAC,CAAC;QACxE,OAAO,GAAG,CAAC;IACb,CAAC;CACF", "sourcesContent": ["export class DitamapUtils {\r\n  /**\r\n   * Checks if an html element is a mapref or part\r\n   * @param element\r\n   */\r\n  static isMapRefLike(element: HTMLElement): boolean {\r\n    const hasDitamapFormat = element.getAttribute(\"format\") == \"ditamap\";\r\n    const hasHref = element.getAttribute(\"href\");\r\n    return Boolean(hasDitamapFormat && hasHref);\r\n  }\r\n\r\n  /**\r\n   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)\r\n   * @param element\r\n   */\r\n  static isTopicRefLike(element: HTMLElement) {\r\n    if (\r\n      element.tagName.toLowerCase() === \"part\" ||\r\n      element.tagName.toLowerCase() === \"mapref\" ||\r\n      element.tagName.toLowerCase() === \"bookmap\" ||\r\n      element.tagName.toLowerCase() === \"map\"\r\n    )\r\n      return false;\r\n\r\n    //add support for \"mapgroup-d/topichead\" with no\r\n    var hasHref = false;\r\n    var notExternalScope = false;\r\n    var hasTopicRef = false;\r\n    var hasTopicHead = false;\r\n    var specialCBCase = false;\r\n    var notKeydef = true;\r\n    var notResourceOnly = true;\r\n\r\n    if (element.classList) {\r\n      hasTopicRef = element.classList.contains(\"map/topicref\");\r\n      hasTopicHead = element.classList.contains(\"mapgroup-d/topichead\");\r\n    }\r\n\r\n    notResourceOnly = element.getAttribute(\"resource-only\") != \"resource-only\";\r\n    notKeydef = element.tagName.toLowerCase() != \"keydef\";\r\n\r\n    hasHref = element.getAttribute(\"href\") != null;\r\n\r\n    if (element.nodeName == \"topicref\" || element.nodeName == \"chapter\") {\r\n      if (!hasHref) {\r\n        specialCBCase = true;\r\n      }\r\n    }\r\n\r\n    notExternalScope = element.getAttribute(\"scope\") != \"external\";\r\n\r\n    if (\r\n      (hasTopicRef &&\r\n        hasHref &&\r\n        notExternalScope &&\r\n        notResourceOnly &&\r\n        notKeydef) ||\r\n      hasTopicHead ||\r\n      specialCBCase\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the href used to identify the map, if no href assume it is the root map\r\n   * @param element - xml element\r\n   * @param rootMapName - name of the root map\r\n   */\r\n  static getMapName(\r\n    element: HTMLElement,\r\n    rootMapName?: string\r\n  ): string | undefined {\r\n    if (element.getAttribute(\"href\")) {\r\n      return element.getAttribute(\"href\") as string;\r\n    }\r\n\r\n    if (\r\n      rootMapName &&\r\n      (element.tagName.toLowerCase() === \"bookmap\" ||\r\n        element.tagName.toLowerCase() === \"map\")\r\n    ) {\r\n      return rootMapName;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  static getMapTitle(element: HTMLElement) {\r\n    return DitamapUtils.removeXmlNoise(\r\n      DitamapUtils.getDirtyTitle(element)\r\n    ).trim();\r\n  }\r\n\r\n  static getDirtyTitle(element: HTMLElement) {\r\n    let mainbooktitleEl = element.querySelector(\"mainbooktitle\");\r\n    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;\r\n\r\n    let titleEl = element.querySelector(\"title\");\r\n    if (titleEl) return titleEl.innerText || titleEl.innerHTML;\r\n\r\n    let titleAtt = element.getAttribute(\"title\");\r\n    if (titleAtt) return titleAtt;\r\n\r\n    return element.tagName;\r\n  }\r\n\r\n  static removeXmlNoise(str: string) {\r\n    if (str)\r\n      return str.replace(/<\\?xm-replace_text\\s+Main Book Title\\s*\\?>/g, \"\");\r\n    return str;\r\n  }\r\n}\r\n"]}