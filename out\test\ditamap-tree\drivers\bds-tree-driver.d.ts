/** copied from tree-v2 */
export declare class TreeTestDriver {
    el: HTMLElement;
    constructor(el: HTMLElement);
    getNodeByKey(key: string): Promise<TreeTestDriver>;
    get spantEl(): HTMLElement;
    get expander(): HTMLElement;
    get label(): string;
    get isSelected(): boolean;
    get isExpanded(): boolean;
    expandNode(): void;
    click(): void;
    addEventListener(event: string, cb: (e: any) => void): void;
}
export declare const sleep: (time: number) => Promise<void>;
