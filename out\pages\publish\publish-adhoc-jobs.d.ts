import { LitElement } from "lit";
import "../../components/header/publish";
import "../../components/publish/publish-outputs-pane";
export declare class WexPagePublishAdhocJobs extends LitElement {
    outputJobs: any[];
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    _refreshOutputJobs: () => Promise<void>;
    _renderMainPane(): import("lit").TemplateResult<1>;
    render(): import("lit").TemplateResult<1>;
}
