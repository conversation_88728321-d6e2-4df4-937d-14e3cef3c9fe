import { LitElement } from "lit";
import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
export declare class WexListPicker extends LitElement {
    selected: Array<any>;
    unselected: Array<any>;
    _state: any;
    _string: any;
    _actions: Array<any>;
    private _subscription;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    private init;
    private stateChange;
    private handleSelect;
    private handleAction;
    protected render(): import("lit").TemplateResult<1>;
}
