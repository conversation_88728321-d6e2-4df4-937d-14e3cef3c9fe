export const OXYGEN_SAMPLE_BOOKMAP = `<bookmap id="taskbook">
 <booktitle>
   <mainbooktitle>Product tasks</mainbooktitle>
   <booktitlealt>Tasks and what they do</booktitlealt>
 </booktitle>
 <bookmeta>
   <author><PERSON></author>
   <bookrights>
     <copyrfirst>
       <year>2006</year>
     </copyrfirst>
     <bookowner>
       <person href="janedoe.dita"><PERSON></person>
     </bookowner>
   </bookrights>
 </bookmeta>
 <frontmatter>
   <preface/>
 </frontmatter>
   <chapter format="ditamap" href="installing.ditamap"/>
   <chapter href="configuring.dita"/>
   <chapter href="maintaining.dita">
     <topicref href="maintainstorage.dita"/>
     <topicref href="maintainserver.dita"/>
     <topicref href="maintaindatabase.dita"/>
   </chapter>
 <appendix href="task_appendix.dita"/>
</bookmap>`;

export const INSTALLING_DITAMAP = `<map title="Installing">
  <topicref href="install_overview.dita"/>
  <topicref href="system_requirements.dita"/>
  <topicref href="download_software.dita"/>
  <topicref href="install_steps.dita">
    <topicref href="install_step1.dita"/>
    <topicref href="install_step2.dita"/>
    <topicref href="install_step3.dita"/>
  </topicref>
  <topicref href="verify_installation.dita"/>
</map>`;
