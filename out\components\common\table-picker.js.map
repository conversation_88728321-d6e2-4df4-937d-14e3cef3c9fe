{"version": 3, "file": "table-picker.js", "sourceRoot": "", "sources": ["../../../src/components/common/table-picker.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,oBAAoB,CAAC;AAC5B,OAAO,cAAc,CAAC;AAEtB,mEAAmE;AACnE,iEAAiE;AACjE,qDAAqD;AAG9C,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,UAAU;IAAvC;;QACsB,aAAQ,GAAU,EAAE,CAAC;QACrB,eAAU,GAAU,EAAE,CAAC;QACzC,UAAK,GAAwB,EAAE,CAAC;QAChC,WAAM,GAAQ,EAAE,CAAC;QACjB,YAAO,GAAe,EAAE,CAAC;QAE1B,YAAO,GAAU,EAAE,CAAC;IA6W9B,CAAC;IA3WC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqJT,CAAC;IACJ,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAEO,IAAI,CAAC,KAA0B;QACrC,IAAI,CAAC,OAAO,GAAG;YACb,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,mBAAmB,EAAE;YAC/D,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,iBAAiB,EAAE;SAClE,CAAC;QACF,IAAI,CAAC,OAAO,GAAG;YACb;gBACE,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,OAAO;gBACd,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,IAAI;aACX;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,IAAI;aACX;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,IAAI;aACX;SACF,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,KAA0B;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,CAAc;QACnD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;YACtB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,IAAI,GACR,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YAElE,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;oBACnB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;oBAC5B,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ;wBAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACjE,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBAEpB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC9B,OAAO;YACT,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACxB,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBACzC,GAAG,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC9B,CAAC;gBAAS,CAAC;YACT,gCAAgC;QAClC,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY,EAAE,GAAW;QAC5C,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAClD,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,OAAO,CAAC,CAAC,CAAC;gBAC3D,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,mBAAmB,EAAE;YACnC,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;YACD,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,aAAa;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAA;;UAEf,IAAI,CAAC,OAAO,CAAC,GAAG,CAChB,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAA;;sBAEJ,MAAM,CAAC,IAAI;uBACV,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;wBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;;WAEtD,CACF;;KAEJ,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,SAAS;QACf,MAAM,QAAQ,GAAG,IAAI,CAAA;;gBAET,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;;qBAEpB,IAAI,CAAC,OAAO;mBACd,IAAI,CAAC,UAAU;0BACR,KAAK;oBACX,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;;;;KAI7D,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,YAAY;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAA;;gBAET,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;;qBAEnB,IAAI,CAAC,OAAO;mBACd,IAAI,CAAC,QAAQ;0BACN,KAAK;oBACX,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;;;KAG7D,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAES,MAAM;QACd,OAAO,IAAI,CAAA;;UAEL,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;;KAEpE,CAAC;IACJ,CAAC;CACF,CAAA;AAnX4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gDAAsB;AACrB;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;kDAAwB;AACzC;IAAR,KAAK,EAAE;6CAAiC;AAChC;IAAR,KAAK,EAAE;8CAAkB;AACjB;IAAR,KAAK,EAAE;+CAA0B;AALvB,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAoX1B", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\nimport \"../base/fixed-item\";\r\nimport \"../wex-table\";\r\n\r\n// objects in the arrays must have a name key to function correctly\r\n// the primary use case is currently to update values on a pubdef\r\n// emits both selected and unselected on every change\r\n\r\n@customElement(\"wex-table-picker\")\r\nexport class WexTablePicker extends LitElement {\r\n  @property({ type: Array }) selected: any[] = [];\r\n  @property({ type: Array }) unselected: any[] = [];\r\n  @state() state: Record<string, any> = {};\r\n  @state() string: any = {};\r\n  @state() actions: Array<any> = [];\r\n  private subscription: any;\r\n  private columns: any[] = [];\r\n\r\n  static get styles() {\r\n    return css`\r\n      :host {\r\n        display: flex;\r\n        flex: 1;\r\n        width: 100%;\r\n        height: 100%;\r\n        min-height: 0;\r\n        max-height: 100%;\r\n        box-sizing: border-box;\r\n        overflow: hidden;\r\n      }\r\n\r\n      /* Ensure the main column container fills the host */\r\n      wex-col-item {\r\n        flex: 1;\r\n        width: 100%;\r\n        height: 100%;\r\n        min-height: 0;\r\n        max-height: 100%;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: hidden;\r\n      }\r\n\r\n      /* Fixed height tables with light border */\r\n      wex-table {\r\n        height: 140px;\r\n        min-height: 140px;\r\n        max-height: 140px;\r\n        border: 1px solid #e0e0e0;\r\n        border-radius: 4px;\r\n        overflow: auto;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .italic {\r\n        font-style: italic;\r\n      }\r\n\r\n      #dialog {\r\n        width: min(90vw, 600px);\r\n      }\r\n\r\n      .dialog-content {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        padding: 1rem;\r\n        height: min(50vh, 300px);\r\n        color: var(--font-color);\r\n      }\r\n\r\n      .dialog-content * {\r\n        color: var(--font-color);\r\n      }\r\n\r\n      .dialog-content > *:not(:last-child) {\r\n        margin-right: 0.5rem;\r\n      }\r\n\r\n      ul {\r\n        flex-grow: 1;\r\n        margin: 0;\r\n        padding: 0;\r\n        list-style: none;\r\n        border: 1px solid var(--clr-gray-light);\r\n        overflow-y: auto;\r\n        max-height: calc(2.25rem * 8);\r\n        height: 30vh;\r\n      }\r\n\r\n      li {\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 1rem;\r\n        height: 2.25rem;\r\n        border-bottom: 1px solid var(--clr-gray-light);\r\n      }\r\n\r\n      li > span {\r\n        flex-grow: 1;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n\r\n      li > .icon-container {\r\n        position: relative;\r\n        width: 1.5rem;\r\n        height: 1.5rem;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 8px;\r\n      }\r\n\r\n      li > .icon-container:hover {\r\n        background: var(--clr-white);\r\n      }\r\n\r\n      li > *:not(:last-child) {\r\n        margin-right: 1rem;\r\n      }\r\n\r\n      li[active] {\r\n        background: var(--row-selected-background);\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n\r\n      li:not([active]):hover {\r\n        background: var(--clr-gray-ultra-light);\r\n      }\r\n\r\n      .select-column {\r\n        display: flex;\r\n        flex-direction: column;\r\n        width: 100%;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .select-column > span {\r\n        margin-bottom: 0.25rem;\r\n        font-weight: 500;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .select-actions {\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        align-items: center;\r\n        padding: 0.25rem 0;\r\n        gap: 1rem;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .select-actions > * {\r\n        cursor: pointer;\r\n        padding: 0.5rem;\r\n        border-radius: 4px;\r\n        transition: background-color 0.2s;\r\n      }\r\n\r\n      .select-actions > *:hover {\r\n        background-color: var(--clr-gray-ultra-light, #f5f5f5);\r\n      }\r\n    `;\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this.init(state);\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  private init(state: Record<string, any>) {\r\n    this.actions = [\r\n      { name: \"select\", label: \"_select\", icon: \"vaadin:angle-down\" },\r\n      { name: \"deselect\", label: \"_deselect\", icon: \"vaadin:angle-up\" },\r\n    ];\r\n    this.columns = [\r\n      {\r\n        field: \"name\",\r\n        label: \"_name\",\r\n        isSortable: false,\r\n        sort: null,\r\n      },\r\n      {\r\n        field: \"presProfileName\",\r\n        label: \"_presProfile\",\r\n        isSortable: false,\r\n        sort: null,\r\n      },\r\n      {\r\n        field: \"procProfileName\",\r\n        label: \"_procProfile\",\r\n        isSortable: false,\r\n        sort: null,\r\n      },\r\n    ];\r\n  }\r\n\r\n  private stateChange(state: Record<string, any>) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n  }\r\n\r\n  async _handleDefRowClick(type: string, e: CustomEvent) {\r\n    try {\r\n      const data = e.detail;\r\n      if (!data) return;\r\n\r\n      const obj = { ...data.value };\r\n      console.log(\"_handleDefRowClick\", obj.rowId);\r\n\r\n      const rows =\r\n        type === \"selected\" ? [...this.selected] : [...this.unselected];\r\n\r\n      if (e.detail.ctrlKey) {\r\n        rows.forEach((row) => {\r\n          row.selected = row.selected;\r\n          if (row.pubDefId == obj.pubDefId) row.selected = !row.selected;\r\n        });\r\n        this.pubDefs = rows;\r\n\r\n        this._selectedPubDefId = null;\r\n        return;\r\n      }\r\n\r\n      rows.forEach((row, idx) => {\r\n        console.log(\"row\", row.rowId, obj.rowId);\r\n        row.selected = idx + 1 === obj.rowId;\r\n      });\r\n\r\n      type === \"selected\" ? (this.selected = rows) : (this.unselected = rows);\r\n    } catch (err) {\r\n      console.error(\"error\", err);\r\n    } finally {\r\n      // Handle any cleanup or updates\r\n    }\r\n  }\r\n\r\n  private handleSelect(type: string, idx: number) {\r\n    if (type === \"selected\") {\r\n      const list = JSON.parse(JSON.stringify(this.selected));\r\n      list[idx].active = !list[idx].active;\r\n      this.selected = list;\r\n    } else if (type === \"deselect\") {\r\n      const list = JSON.parse(JSON.stringify(this.unselected));\r\n      list[idx].active = !list[idx].active;\r\n      this.unselected = list;\r\n    }\r\n  }\r\n\r\n  private handleAction(type: string) {\r\n    if (type === \"select\") {\r\n      let a = this.unselected.filter((x) => x.selected);\r\n      a = this.selected.concat(a);\r\n      this.selected = JSON.parse(JSON.stringify(a));\r\n      this.selected.forEach((x) => (x.selected = false));\r\n      this.selected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      let b = this.unselected.filter((x) => !x.selected);\r\n      this.unselected = JSON.parse(JSON.stringify(b));\r\n      this.unselected.forEach((x) => (x.selected = false));\r\n      this.unselected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      this.requestUpdate();\r\n    }\r\n\r\n    if (type === \"deselect\") {\r\n      let a = this.selected.filter((x) => x.selected);\r\n      a = this.unselected.concat(a);\r\n      this.unselected = JSON.parse(JSON.stringify(a));\r\n      this.unselected.forEach((x) => (x.selected = false));\r\n      this.unselected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      let b = this.selected.filter((x) => !x.selected);\r\n      this.selected = JSON.parse(JSON.stringify(b));\r\n      this.selected.forEach((x) => (x.selected = false));\r\n      this.selected.sort((a, b) => {\r\n        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;\r\n        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;\r\n        return 0;\r\n      });\r\n\r\n      this.requestUpdate();\r\n    }\r\n\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"pick-list-updated\", {\r\n        detail: {\r\n          selected: this.selected,\r\n          unselected: this.unselected,\r\n        },\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n  }\r\n\r\n  private renderActions() {\r\n    const template = html`\r\n      <div class=\"select-actions\">\r\n        ${this.actions.map(\r\n          (action) => html`\r\n            <iron-icon\r\n              icon=\"${action.icon}\"\r\n              title=\"${this.string[action.label]}\"\r\n              @click=\"${this.handleAction.bind(this, action.name)}\"\r\n            ></iron-icon>\r\n          `\r\n        )}\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderTop() {\r\n    const template = html`\r\n      <div class=\"select-column\">\r\n        <span>${this.string[\"_available\"]}</span>\r\n        <wex-table\r\n          .columns=${this.columns}\r\n          .rows=\"${this.unselected}\"\r\n          .enableSelect=${false}\r\n          @click=\"${this._handleDefRowClick.bind(this, \"deselect\")}\"\r\n        >\r\n        </wex-table>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  private renderBottom() {\r\n    const template = html`\r\n      <div class=\"select-column\">\r\n        <span>${this.string[\"_selected\"]}</span>\r\n        <wex-table\r\n          .columns=${this.columns}\r\n          .rows=\"${this.selected}\"\r\n          .enableSelect=${false}\r\n          @click=\"${this._handleDefRowClick.bind(this, \"selected\")}\"\r\n        ></wex-table>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  protected render() {\r\n    return html`\r\n      <wex-col-item justifyContent=\"stretch\">\r\n        ${this.renderTop()} ${this.renderActions()} ${this.renderBottom()}\r\n      </wex-col-item>\r\n    `;\r\n  }\r\n}\r\n"]}