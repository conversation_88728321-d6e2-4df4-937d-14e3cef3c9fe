import { LitElement } from "lit";
import "../base/col-item";
import "../base/row-item";
import "../base/icon";
import "../wex-table";
import "./definitions-filter-bar";
import "./definitions-action-bar";
import "@ui5/webcomponents/dist/Button.js";
export declare class WexPublishDefinitionsPane extends LitElement {
    state: any;
    string: any;
    projects: any[];
    pubDefs: any[];
    _isLoading: boolean;
    activeFilters: any;
    filterOptions: any;
    private subscription;
    pubDefColumns: any[];
    categories: any[];
    _selectedProjectId: number | null;
    _selectedPubDefId: number | null;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    stateChange(state: any): void;
    _init(): Promise<void>;
    updated(changedProps: Map<string | number | symbol, unknown>): void;
    _handleDefRowClick(e: CustomEvent): Promise<void>;
    _renderComboBar(): import("lit").TemplateResult<1>;
    _handleSetActiveFilters(e: CustomEvent): void;
    _handleSelectAllDefinitions: () => void;
    _handleClearSelectedDefinitions: () => void;
    _renderDefsTable(): import("lit").TemplateResult<1>;
    render(): import("lit").TemplateResult<1>;
}
