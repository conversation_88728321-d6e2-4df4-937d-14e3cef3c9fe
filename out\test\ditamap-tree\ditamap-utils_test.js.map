{"version": 3, "file": "ditamap-utils_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap-utils_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAExC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;kDAC0C,EAC1C,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;uDAC+C,EAC/C,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,2EAA2E,EAC3E,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACzB,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,8FAA8F,EAC9F,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,gGAAgG,EAChG,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;;8CAEsC,EACtC,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE;QACvB,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;kDAC0C,EAC1C,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IAC5E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC1D,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;;8CAEsC,EACtC,iBAAiB,CAClB,CAAC;QACF,MAAM,CACJ,YAAY,CAAC,UAAU,CACrB,QAAQ,CAAC,eAAe,EACxB,4CAA4C,CAC7C,CACF,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { DitamapUtils } from \"../../lib/jsDitamap/ditamap-utils\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\n  describe(\"Is Map reference\", () => {\r\n    it(\"part with href and format=ditamap => true\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n          class=\"- map/topicref bookmap/part \" />`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;\r\n    });\r\n  \r\n    it(\"mapref with href and format=ditamap => true\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<mapref format=\"ditamap\" href=\"/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap\"\r\n          class=\"+ map/topicref mapgroup-d/mapref \" />`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;\r\n    });\r\n  });\r\n  \r\n  describe(\"Is TopicRefLike\", () => {\r\n    it(\"topicref => true\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<topicref href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \" />`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n    });\r\n  \r\n    it(\"chapter => true\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<chapter href=\"/Content/Introduction_xi1674_1_1.xml\" class=\"- map/topicref bookmap/chapter\">`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n    });\r\n  \r\n    it(\"topichead => true\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<topichead href=\"/Content/Introduction_xi1674_1_1.xml\" class=\"- map/topicref bookmap/chapter\">`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n    });\r\n  \r\n    it(\"bookmap => false\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n          id=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n          class=\"- map/map bookmap/bookmap \">`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;\r\n    });\r\n  \r\n    it(\"part => false\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n          class=\"- map/topicref bookmap/part \" />`,\r\n        \"application/xml\"\r\n      );\r\n      expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;\r\n    });\r\n  });\r\n  \r\n  describe(\"Get map name (href)\", () => {\r\n    it(\"If the element is a bookmap, use the rootMapName\", () => {\r\n      let document = new DOMParser().parseFromString(\r\n        `<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n          id=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n          class=\"- map/map bookmap/bookmap \">`,\r\n        \"application/xml\"\r\n      );\r\n      expect(\r\n        DitamapUtils.getMapName(\r\n          document.documentElement,\r\n          \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n        )\r\n      ).to.equal(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\");\r\n    });\r\n  });"]}