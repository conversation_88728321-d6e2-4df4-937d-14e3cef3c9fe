import { LitElement, TemplateResult } from "lit";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/List.js";
export declare class WexDialogProjectSelector extends LitElement {
    constructor();
    connectedCallback(): void;
    disconnectedCallback(): void;
    stateChange(state: any): void;
    prepData(): void;
    myRender(): void;
    _projectCatSelectorSelected(val: any): boolean;
    projectSelectorDialogTemplate(): TemplateResult<1>;
    _allOption(): TemplateResult<1>;
    _setProjectCatSelected(e: any): void;
    _selectProject(e: any): void;
    _saveProject(e: any): void;
    closeDialog(): void;
}
