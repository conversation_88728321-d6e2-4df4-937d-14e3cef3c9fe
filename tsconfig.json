{"compilerOptions": {"target": "es2021", "module": "es2020", "baseUrl": "./src", "paths": {"store/*": ["store/*"], "resources/*": ["resources/*"], "lib/*": ["lib/*"]}, "lib": ["es2021", "DOM", "DOM.Iterable"], "declaration": true, "declarationMap": false, "sourceMap": true, "inlineSources": true, "outDir": "./out", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "useDefineForClassFields": false, "forceConsistentCasingInFileNames": true, "plugins": [{"name": "ts-lit-plugin", "strict": true}], "types": ["mocha"]}, "include": ["src/**/*.ts", "src/test/ditamap-tree/mockServiceWorker.js", "mockServiceWorker.ts"], "exclude": []}