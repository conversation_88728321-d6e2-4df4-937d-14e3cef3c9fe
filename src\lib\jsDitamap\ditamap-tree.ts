import { FileMeta } from "@bds/types";
import {
  TopicReferenceNode,
  DitamapNode,
  MapReferenceNode,
  RootMapNode,
  StructuralNode,
} from "./ditamap-node";
import { DitamapUtils } from "./ditamap-utils";
import { v4 as uuidv4 } from "uuid";

export class DitamapTree {
  /**
   * Context containing workspace, metadata, and other shared state
   */
  context: DitamapContext;
  /**
   * Underlying tree data structure
   */
  root: DitamapNode | null = null;
  /**
   * Name of the root map
   */
  rootMapName: string;

  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {
    this.context = {
      workspace: files ?? new Map(),
      metadata: new Map(),
      dirty: new Set(),
      buffer: null,
      cutId: null,
    };
    this.rootMapName = rootMapName;
  }

  getRoot() {
    return this.root;
  }

  async load() {
    await this._fetchMap(this.rootMapName);
    this.root = await this.buildTree(this.rootMapName);
    await this._fetchMetadata([...this.context.metadata.keys()].join(","));
  }

  async rebuildTree() {
    await this.load();
  }

  async buildTree(mapName: string): Promise<DitamapNode | null> {
    if (this.context.workspace.has(mapName)) {
      return await this._treeBfs(this.context.workspace.get(mapName)!);
    }
    return null;
  }

  /**
   * Builds json tree from xml
   * @param rootHtml - parsed xml element
   */
  async _treeBfs(rootHtml: HTMLElement): Promise<DitamapNode> {
    let tree = new RootMapNode({
      rootElement: rootHtml,
      rootMapName: this.rootMapName,
      context: this.context,
    });

    let Q: QueueItem[] = [[rootHtml, tree, this.rootMapName]];
    while (Array.isArray(Q) && Q.length > 0) {
      let [element, parent, mapName] = Q.shift() as QueueItem;

      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {
        switch (DitamapUtils.getDitamapNodeClass(child)) {
          case "MapReferenceNode": {
            let structuralEl = await this._getStructuralElement(
              child.getAttribute("href")!
            );

            if (!structuralEl) {
              console.warn("No structural element found for", child);
            }

            structuralEl = structuralEl ?? child;

            let newMapReferenceNode = new MapReferenceNode({
              refElement: child,
              structuralElement: structuralEl,
              containingMap: mapName,
              context: this.context,
            });

            let newMapName = DitamapUtils.getMapName(child, this.rootMapName);
            let mapNameToUse = newMapName ?? mapName;

            if (!newMapName) {
              console.warn("No map name found for", child);
            }

            Q.push([structuralEl, newMapReferenceNode, mapNameToUse]);
            parent.children.push(newMapReferenceNode);
            this._addToMetadataMap(mapNameToUse);
            continue;
          }

          case "StructuralNode": {
            let structuralNode = new StructuralNode(child, mapName);
            Q.push([child, structuralNode, mapName]);
            parent.children.push(structuralNode);
            continue;
          }

          case "TopicReferenceNode": {
            let contentNode = new TopicReferenceNode({
              refElement: child,
              containingMap: mapName,
              context: this.context,
            });
            Q.push([child, contentNode, mapName]);
            parent.children.push(contentNode);
            if (child.getAttribute("href")) {
              this._addToMetadataMap(child.getAttribute("href")!);
            }
            continue;
          }
        }
      }
    }

    return tree;
  }

  _addToMetadataMap(href: string) {
    if (this.context.metadata.has(href)) {
      return;
    }
    this.context.metadata.set(href, null);
  }

  /** Adds ids to xml which will be stable accross tree operations */
  _addIdsToXml(rootEl: HTMLElement): void {
    let Q = [rootEl];

    if (!rootEl.getAttribute("data-id")) {
      rootEl.setAttribute("data-id", uuidv4());
    }

    while (Array.isArray(Q) && Q.length > 0) {
      let el = Q.shift() as HTMLElement;

      if (!el.getAttribute("data-id")) {
        el.setAttribute("data-id", uuidv4());
      }

      for (let child of el.children as HTMLCollectionOf<HTMLElement>) {
        Q.push(child);
      }
    }
  }

  async _getStructuralElement(href: string) {
    if (this.context.workspace.has(href)) {
      return this.context.workspace.get(href);
    }

    await this._fetchMap(href);

    return this.context.workspace.get(href);
  }

  /** -- Fetch calls -- */

  async _fetchMetadata(filenames: string) {
    try {
      const response = await fetch(
        `/lwa/jrest/GetMetaDataForResList?cdlResLblIds=${filenames}`
      );
      if (!response.ok) {
        throw Error();
      }
      const data: FileMeta[] = await response.json();
      for (let file of data) {
        this.context.metadata.set(file.resLblId, file);
      }
    } catch (e) {
      console.log(e);
    }
  }

  async _fetchMap(reslblId: string) {
    if (this.context.workspace.has(reslblId)) {
      return;
    }

    try {
      const response = await fetch(`/lwa/jrest/GetTopicRenderXml${reslblId}`);
      if (!response.ok) {
        throw Error();
      }

      let textResponse = await response.text();
      if (!textResponse) {
        throw Error();
      }

      let parser = new DOMParser();
      let xml = parser.parseFromString(textResponse, "application/xml");
      this._addIdsToXml(xml.documentElement); // Add ids to xml
      this.context.workspace.set(reslblId, xml.documentElement);

      // Find submaps
      let iterator = xml.evaluate(
        '//*[@format="ditamap"]',
        xml.documentElement,
        null,
        XPathResult.ORDERED_NODE_ITERATOR_TYPE,
        null
      );

      let current = iterator.iterateNext();
      while (current) {
        if (current.getAttribute("href")) {
          await this._fetchMap(current.getAttribute("href"));
        }
        current = iterator.iterateNext();
      }
    } catch (e) {
      console.log(e);
    }
  }

  // -- Helpers --
}

type QueueItem = [HTMLElement, DitamapNode, string];

export interface DitamapContext {
  workspace: Map<string, HTMLElement>;
  metadata: Map<string, FileMeta | null>;
  dirty: Set<string>;
  cutId: string | null;
  buffer: DitamapNode | null;
}

/**
 *
 * The traversal should not include map or bookmap elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
