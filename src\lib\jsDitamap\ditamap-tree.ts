import {
  ContentNode,
  DitamapNode,
  MapRefLikeElementNode,
  RootMapNode,
} from "./ditamap-node";

export class DitamapTree {
  /**
   * Map of all the stored ditamap xml
   */
  workspace: Map<string, HTMLElement>;
  /**
   * Underlying tree data structure
   */
  root: DitamapNode | null = null;
  /**
   * Name of the root map
   */
  rootMapName: string;

  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {
    this.workspace = files ?? new Map();
    this.root = this.buildTree(rootMapName);
    this.rootMapName = rootMapName;
  }

  buildTree(mapName: string): DitamapNode | null {
    if (this.workspace.has(mapName)) {
      return this._treeBfs(this.workspace.get(mapName)!);
    }
    return null;
  }

  /**
   * Builds json tree from xml
   * @param rootHtml - parsed xml element
   */
  _treeBfs(rootHtml: HTMLElement): DitamapNode {
    let tree = new RootMapNode(rootHtml, this.rootMapName);

    let Q: QueueItem[] = [[rootHtml, tree, this.rootMapName]];
    while (Array.isArray(Q) && Q.length > 0) {
      let [element, parent, mapName] = Q.shift() as QueueItem;

      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {
        if (DitamapTree.isMapRefLike(child)) {
          let structuralEl = this.workspace.get(
            child.getAttribute("href") as string
          );

          if (!structuralEl) {
            console.warn("No structural element found for", child);
          }

          structuralEl = structuralEl ?? child;

          let newMapRefLikeNode = new MapRefLikeElementNode(
            child,
            structuralEl,
            mapName
          );

          let newMapName = DitamapTree.getMapName(child, this.rootMapName);
          let mapNameToUse = newMapName ?? mapName;

          if (!newMapName) {
            console.warn("No map name found for", child);
          }

          Q.push([structuralEl, newMapRefLikeNode, mapNameToUse]);
          parent.children.push(newMapRefLikeNode);
        }

        if (DitamapTree.isContent(child)) {
          let contentNode = new ContentNode(child, mapName);
          Q.push([child, contentNode, mapName]);
          parent.children.push(contentNode);
        }
      }
    }

    return tree;
  }

  //---- Helpers ----
  /**
   * Checks if an html element is a mapref or part
   * @param element
   */
  static isMapRefLike(element: HTMLElement): boolean {
    const hasDitamapFormat = element.getAttribute("format") == "ditamap";
    const hasHref = element.getAttribute("href");
    return Boolean(hasDitamapFormat && hasHref);
  }

  /**
   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
   * @param element
   */
  static isContent(element: HTMLElement): boolean {
    return element.tagName === "topicref" || element.tagName === "chapter";
  }

  /**
   * Gets the href used to identify the map, if no href assume it is the root map
   * @param element - xml element
   * @param rootMapName - name of the root map
   */
  static getMapName(
    element: HTMLElement,
    rootMapName?: string
  ): string | undefined {
    if (element.getAttribute("href")) {
      return element.getAttribute("href") as string;
    }

    if (
      rootMapName &&
      (element.tagName.toLowerCase() === "bookmap" ||
        element.tagName.toLowerCase() === "map")
    ) {
      return rootMapName;
    }

    return undefined;
  }

  static getMapTitle(element: HTMLElement) {
    let mainbooktitleEl = element.querySelector("mainbooktitle");
    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;

    let titleEl = element.querySelector("title");
    if (titleEl) return titleEl.innerText || titleEl.innerHTML;

    let titleAtt = element.getAttribute("title");
    if (titleAtt) return titleAtt;

    return element.tagName;
  }

  static removeXmlNoise(str: string) {
    if (str) return str.replace("<?xm-replace_text Main Book Title ?>", "");
    return str;
  }
}

type QueueItem = [HTMLElement, DitamapNode, string];

// const structuralElements = new Set([
//   "map",
//   "bookmap",
//   "topichead",
//   "topicgroup",
// ]);

// const referenceElements = new Set(["mapref", "part", "keydef"]);

// const dualUseElements = new Set(["topicref", "chapter", "appendix"]);

/**
 *
 * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
