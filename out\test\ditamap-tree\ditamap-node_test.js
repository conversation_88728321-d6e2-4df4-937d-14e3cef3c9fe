import { MapRefLikeElementNode, RootMapNode } from "../../lib/jsDitamap/ditamap-node";
import { expect } from "@open-wc/testing";
describe("Root Map Node", () => {
    it("should create node from bookmap", () => {
        let element = new DOMParser().parseFromString(`
          <bookmap 
          class="- map/map bookmap/bookmap ">
              <booktitle class="- topic/title bookmap/booktitle ">
          <mainbooktitle class="- topic/ph bookmap/mainbooktitle ">
              <?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>
      </booktitle>
          </bookmap>
            `, "application/xml").documentElement;
        let node = new RootMapNode(element, "/Content/Phone1_Bookmap_xi1577_1_1.ditamap");
        expect(node).to.deep.equal({
            href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            containingMap: null,
            title: "Phone 1 User Guide",
            refElement: null,
            structuralElement: element,
            mapPath: [],
            children: [],
        });
    });
});
describe("Map Ref Like Node", () => {
    it("should create node from part", () => {
        let element = new DOMParser().parseFromString(`
        <part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
          class="- map/topicref bookmap/part " />
      `, "application/xml").documentElement;
        let structuralElement = new DOMParser().parseFromString(`
          <map title="Submap Phones"
      class="- map/map "></map>
        `, "application/xml").documentElement;
        let node = new MapRefLikeElementNode(element, structuralElement, "/Content/Phone1_Bookmap_xi1577_1_1.ditamap");
        expect(node).to.deep.equal({
            href: "/Content/submap_phones_xi1609_1_1.ditamap",
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            title: "Submap Phones",
            refElement: element,
            structuralElement: structuralElement,
            mapPath: [],
            children: [],
        });
    });
});
//# sourceMappingURL=ditamap-node_test.js.map