import { TopicReferenceNode } from "../../lib/jsDitamap/ditamap-node";
import { createXmlElement } from "./data/elements";
import { SUBMAP_PHONES } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";
let emptyContext = {
    workspace: new Map(),
    metadata: new Map(),
    dirty: new Set(),
    cutId: null,
    buffer: null,
};
describe("Rootmap", () => {
    it.skip("Blocks rootmap deletion", () => { });
});
describe("Map Reference", () => {
    it.skip("Deletes mapref", () => { });
    it.skip("Deletes part", () => { });
});
describe("Topic Reference", () => {
    it("Deletes topicref", () => {
        let el = createXmlElement(SUBMAP_PHONES);
        let node = new TopicReferenceNode({
            refElement: el,
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            context: emptyContext,
        });
        // node.remove();
        expect(node?.structuralElement).to.be.null;
        expect(node?.refElement).to.be.null;
    });
    it.skip("Deletes chapter", () => { });
});
describe("Structural Element", () => {
    it.skip("Deletes topichead", () => { });
});
//# sourceMappingURL=delete_test.js.map