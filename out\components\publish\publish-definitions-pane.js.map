{"version": 3, "file": "publish-definitions-pane.js", "sourceRoot": "", "sources": ["../../../src/components/publish/publish-definitions-pane.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACzD,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,2CAA2C;AAE3C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,cAAc,CAAC;AACtB,OAAO,cAAc,CAAC;AAEtB,OAAO,0BAA0B,CAAC;AAClC,OAAO,0BAA0B,CAAC;AAElC,OAAO,mCAAmC,CAAC;AAGpC,IAAM,yBAAyB,GAA/B,MAAM,yBAA0B,SAAQ,UAAU;IAAlD;;QACI,UAAK,GAAQ,IAAI,CAAC;QAClB,WAAM,GAAQ,IAAI,CAAC;QACnB,aAAQ,GAAU,EAAE,CAAC;QACrB,YAAO,GAAU,EAAE,CAAC;QACpB,eAAU,GAAY,KAAK,CAAC;QAC5B,kBAAa,GAAQ,EAAE,CAAC;QACxB,kBAAa,GAAQ,EAAE,CAAC;QACzB,iBAAY,GAAQ,IAAI,CAAC;QACxB,kBAAa,GAAU,EAAE,CAAC;QAC1B,eAAU,GAAU,EAAE,CAAC;QACvB,uBAAkB,GAAkB,IAAI,CAAC;QACzC,sBAAiB,GAAkB,IAAI,CAAC;QA2JjD,gCAA2B,GAAG,GAAG,EAAE;YACjC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC;QAEF,oCAA+B,GAAG,GAAG,EAAE;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC;IAuBJ,CAAC;IAxLC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;KAMT,CAAC;QACF,mBAAmB;IACrB,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,+DAA+D;QAC/D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;QAC3D,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,MAAM,aAAa,CAAC,OAAO,CACzC,+BAA+B,CAChC,CAAC;YACF,IAAI,CAAC,OAAO,GAAG,MAAM,aAAa,CAAC,OAAO,CACxC,8BAA8B,CAC/B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,OAAO,CAAC,YAAoD;QAC1D,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClE,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,wBAAwB,EAAE;oBACxC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACtD,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CACH,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,uBAAuB,EAAE;oBACvC,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CACH,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,CAAc;QACrC,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;YACtB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAE9B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YAE/B,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;oBACnB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;oBAC5B,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ;wBAAE,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACjE,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBAEpB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC9B,OAAO;YACT,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBACnB,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;YAEnE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC9B,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzE,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,kBAAkB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACzD,CAAC;YACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,YAAY;IACZ,eAAe;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;YACtD,OAAO,IAAI,CAAA;oBACG,IAAI,CAAC,QAAQ;mBACd,IAAI,CAAC,OAAO;kCACG,IAAI,CAAC,2BAA2B;sCAC5B,IAAI,CAAC,+BAA+B;6CAC7B,CAAC;YACxC,oCAAoC;YACpC,oDAAoD;YACpD,+BAA+B;YAC/B,iCAAiC;YACjC,qCAAqC;YACrC,mCAAmC;YACnC,uDAAuD;YACvD,6CAA6C;QAC/C,CAAC;aAAM,CAAC;YACN,4CAA4C;YAC5C,OAAO,IAAI,CAAA;mBACE,IAAI,CAAC,OAAO;oBACX,IAAI,CAAC,QAAQ;sBACX,IAAI,CAAC,UAAU;8BACP,IAAI,CAAC,uBAAuB;6CACb,CAAC;YACxC,+BAA+B;YAC/B,mDAAmD;QACrD,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,CAAc;QACpC,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;QACnC,wEAAwE;QAExE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,CAAC,GAAG,CACT,6CAA6C,EAC7C,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC;IAYD,oBAAoB;IACpB,gBAAgB;QACd,OAAO,IAAI,CAAA;;;;yBAIU,IAAI,CAAC,aAAa;oBACvB,IAAI,CAAC,aAAa;iBACrB,IAAI,CAAC,OAAO;iBACZ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;KAE9C,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;UAEL,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;;KAEtD,CAAC;IACJ,CAAC;CACF,CAAA;AArMU;IAAR,KAAK,EAAE;wDAAmB;AAClB;IAAR,KAAK,EAAE;yDAAoB;AACnB;IAAR,KAAK,EAAE;2DAAsB;AACrB;IAAR,KAAK,EAAE;0DAAqB;AACpB;IAAR,KAAK,EAAE;6DAA6B;AAC5B;IAAR,KAAK,EAAE;gEAAyB;AACxB;IAAR,KAAK,EAAE;gEAAyB;AAExB;IAAR,KAAK,EAAE;gEAA2B;AAC1B;IAAR,KAAK,EAAE;6DAAwB;AACvB;IAAR,KAAK,EAAE;qEAA0C;AACzC;IAAR,KAAK,EAAE;oEAAyC;AAZtC,yBAAyB;IADrC,aAAa,CAAC,8BAA8B,CAAC;GACjC,yBAAyB,CAsMrC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// import * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\nimport \"../base/icon\";\r\nimport \"../wex-table\";\r\n\r\nimport \"./definitions-filter-bar\";\r\nimport \"./definitions-action-bar\";\r\n\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\n\r\n@customElement(\"wex-publish-definitions-pane\")\r\nexport class WexPublishDefinitionsPane extends LitElement {\r\n  @state() state: any = null;\r\n  @state() string: any = null;\r\n  @state() projects: any[] = [];\r\n  @state() pubDefs: any[] = [];\r\n  @state() _isLoading: boolean = false;\r\n  @state() activeFilters: any = {};\r\n  @state() filterOptions: any = {};\r\n  private subscription: any = null;\r\n  @state() pubDefColumns: any[] = [];\r\n  @state() categories: any[] = [];\r\n  @state() _selectedProjectId: number | null = null;\r\n  @state() _selectedPubDefId: number | null = null;\r\n\r\n  static get styles() {\r\n    return css`\r\n      :host {\r\n        display: flex;\r\n        flex: 1;\r\n        width: 100%;\r\n      }\r\n    `;\r\n    // max-height: 80%;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this._init();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n\r\n    // Update reactive properties from state so Lit notices changes\r\n    this.pubDefs = state._state?.pages?.publish?.pubDefs || [];\r\n    this.projects = state._state?.pages?.publish?.projects || [];\r\n  }\r\n\r\n  async _init() {\r\n    try {\r\n      this.pubDefColumns = this.state.columns.publishDefs;\r\n      this.projects = await storeInstance.waitFor(\r\n        \"_state.pages.publish.projects\"\r\n      );\r\n      this.pubDefs = await storeInstance.waitFor(\r\n        \"_state.pages.publish.pubDefs\"\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error during initialization:\", error);\r\n    }\r\n  }\r\n\r\n  updated(changedProps: Map<string | number | symbol, unknown>) {\r\n    if (changedProps.has(\"pubDefs\")) {\r\n      if (this.pubDefs.filter((pubDef) => pubDef.selected).length === 1) {\r\n        this.dispatchEvent(\r\n          new CustomEvent(\"single-pubdef-selected\", {\r\n            detail: this.pubDefs.find((pubDef) => pubDef.selected),\r\n            bubbles: true,\r\n            composed: true,\r\n          })\r\n        );\r\n      } else {\r\n        this.dispatchEvent(\r\n          new CustomEvent(\"reset-selected-pubdef\", {\r\n            bubbles: true,\r\n            composed: true,\r\n          })\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  async _handleDefRowClick(e: CustomEvent) {\r\n    try {\r\n      this._isLoading = true;\r\n      const data = e.detail;\r\n      if (!data) return;\r\n\r\n      const obj = { ...data.value };\r\n\r\n      const rows = [...this.pubDefs];\r\n\r\n      if (e.detail.ctrlKey) {\r\n        rows.forEach((row) => {\r\n          row.selected = row.selected;\r\n          if (row.pubDefId == obj.pubDefId) row.selected = !row.selected;\r\n        });\r\n        this.pubDefs = rows;\r\n\r\n        this._selectedPubDefId = null;\r\n        return;\r\n      }\r\n\r\n      rows.forEach((row) => {\r\n        row.selected = row.pubDefId == obj.pubDefId;\r\n      });\r\n      this._selectedPubDefId = rows.find((row) => row.selected).pubDefId;\r\n\r\n      this.pubDefs = rows;\r\n    } catch (err) {\r\n      console.error(\"error\", err);\r\n    } finally {\r\n      this._isLoading = false;\r\n      const selectedPubDefs = this.pubDefs.filter((pubDef) => pubDef.selected);\r\n      if (selectedPubDefs.length === 1) {\r\n        this._selectedProjectId = selectedPubDefs[0].projectId;\r\n      }\r\n      this.requestUpdate();\r\n    }\r\n  }\r\n\r\n  // combo bar\r\n  _renderComboBar() {\r\n    if (this.pubDefs.filter((row) => row.selected).length) {\r\n      return html`<wex-publish-definitions-action-bar\r\n        .projects=${this.projects}\r\n        .pubDefs=${this.pubDefs}\r\n        @select-all-definitions=${this._handleSelectAllDefinitions}\r\n        @clear-selected-definitions=${this._handleClearSelectedDefinitions}\r\n      ></wex-publish-definitions-action-bar>`;\r\n      // } else if (this.pubDefs.length) {\r\n      //   return html`<wex-publish-definitions-filter-bar\r\n      //     .pubDefs=${this.pubDefs}\r\n      //     .projects=${this.projects}\r\n      //     .categories=${this.categories}\r\n      //     @set-active-filters=${(e) =>\r\n      //       (this.activeFilters = e.detail.activeFilters)}\r\n      //   ></wex-publish-definitions-filter-bar>`;\r\n    } else {\r\n      // console.error(\"Cannot render combo bar\");\r\n      return html`<wex-publish-definitions-filter-bar\r\n        .pubDefs=${this.pubDefs}\r\n        .projects=${this.projects}\r\n        .categories=${this.categories}\r\n        @set-active-filters=${this._handleSetActiveFilters}\r\n      ></wex-publish-definitions-filter-bar>`;\r\n      // @set-active-filters=${(e) =>\r\n      //   (this.activeFilters = e.detail.activeFilters)}\r\n    }\r\n  }\r\n\r\n  _handleSetActiveFilters(e: CustomEvent) {\r\n    console.log(\"_handleSetActiveFilters: e.detail\", e.detail);\r\n    const { activeFilters } = e.detail;\r\n    // console.log(\"_handleSetActiveFilters: activeFilters\", activeFilters);\r\n\r\n    this.activeFilters = activeFilters;\r\n    console.log(\r\n      \"_handleSetActiveFilters: this.activeFilters\",\r\n      this.activeFilters\r\n    );\r\n  }\r\n\r\n  _handleSelectAllDefinitions = () => {\r\n    const rows = this.pubDefs.map((row) => ({ ...row, selected: true }));\r\n    this.pubDefs = rows;\r\n  };\r\n\r\n  _handleClearSelectedDefinitions = () => {\r\n    const rows = this.pubDefs.map((row) => ({ ...row, selected: false }));\r\n    this.pubDefs = rows;\r\n  };\r\n\r\n  // definitions table\r\n  _renderDefsTable() {\r\n    return html`\r\n      <wex-table\r\n        id=\"publish-defs-table\"\r\n        defaultHeight=\"75vh\"\r\n        .activeFilters=${this.activeFilters}\r\n        .columns=\"${this.pubDefColumns}\"\r\n        .rows=\"${this.pubDefs}\"\r\n        @click=${this._handleDefRowClick.bind(this)}\r\n      ></wex-table>\r\n    `;\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <wex-col-item justifyContent=\"flex-start\">\r\n        ${this._renderComboBar()} ${this._renderDefsTable()}\r\n      </wex-col-item>\r\n    `;\r\n  }\r\n}\r\n"]}