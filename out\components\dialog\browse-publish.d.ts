import { LitElement } from "lit";
import "../base/col-item";
import "../common/dialog";
import "../common/select";
import "../base/row-item";
interface DialogOpenData {
}
interface PresentationProfile {
    profileLblId: string;
}
interface ProcessingProfile {
    profileLblId: string;
}
interface OutputFormat {
    publishEngineId: string;
    outputTypeId: string;
}
export declare class WexDialogBrowsePublish extends LitElement {
    dialogOpenData: DialogOpenData | null;
    state: any;
    string: any;
    presentationProfile: PresentationProfile | null;
    processingProfile: ProcessingProfile | null;
    outputFormat: OutputFormat | null;
    private subscription;
    private presentationProfiles;
    private processingProfiles;
    private outputFormats;
    private outputExt;
    private outputFileName;
    private selectedFile;
    static styles: import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    protected updated(changedProperties: any): void;
    private setPresentationProfiles;
    private setProcessingProfiles;
    private setOutputFormats;
    private stateChange;
    private handleInputChange;
    private renderBody;
    protected render(): import("lit").TemplateResult<1>;
    private handleSelectionChange;
    private publish;
}
export {};
