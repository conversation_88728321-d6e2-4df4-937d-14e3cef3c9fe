import { LitElement } from "lit";
import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
import "../wex-table";
export declare class WexTablePicker extends LitElement {
    selected: any[];
    unselected: any[];
    state: Record<string, any>;
    string: any;
    actions: Array<any>;
    private subscription;
    private columns;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    private init;
    private stateChange;
    _handleDefRowClick(type: string, e: CustomEvent): Promise<void>;
    private handleSelect;
    private handleAction;
    private renderActions;
    private renderTop;
    private renderBottom;
    protected render(): import("lit").TemplateResult<1>;
}
