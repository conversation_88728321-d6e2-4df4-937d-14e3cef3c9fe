export class DitamapCommandManager {
    constructor(tree, setRoot) {
        this.tree = tree;
        this.setRoot = setRoot;
        this.history = [];
    }
    async execute(command) {
        command.execute();
        this.history.push(command);
        await this.tree.rebuildTree();
        this.setRoot(this.tree.getRoot());
    }
    undo() {
        this.history.pop()?.undo();
    }
}
export class InsertCommand {
    constructor(node, file) {
        this.node = node;
        this.file = file;
    }
    execute() {
        this.node.insertNodeFromFile(this.file);
    }
    undo() {
        console.log("Undo");
    }
}
//# sourceMappingURL=ditamap-command-manager.js.map