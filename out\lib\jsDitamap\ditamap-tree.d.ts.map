{"version": 3, "file": "ditamap-tree.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,WAAW,EAGZ,MAAM,gBAAgB,CAAC;AAExB,qBAAa,WAAW;IACtB;;OAEG;IACH,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACpC;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,IAAI,CAAQ;IAChC;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;gBAER,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC;IAMjE,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI;IAO9C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,WAAW,GAAG,WAAW;IAgD5C;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO;IAMlD;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO;IAI/C;;;;OAIG;IACH,MAAM,CAAC,UAAU,CACf,OAAO,EAAE,WAAW,EACpB,WAAW,CAAC,EAAE,MAAM,GACnB,MAAM,GAAG,SAAS;IAgBrB,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW;IAavC,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM;CAIlC;AAeD;;;GAGG"}