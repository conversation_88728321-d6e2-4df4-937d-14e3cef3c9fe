var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// @ts-ignore
import * as wexlib from "lib/wexlib.js";
import "../common/dialog";
// import "@ui5/webcomponents/dist/Button.js";
// import "@ui5/webcomponents/dist/Dialog.js";
// import "@ui5/webcomponents/dist/Label.js";
// import "@polymer/iron-icons/iron-icons.js";
let WexDialogSelect = class WexDialogSelect extends LitElement {
    constructor() {
        super(...arguments);
        this.dialogOpenData = null;
        this.state = {};
        this.string = {};
        this.subscription = null;
        this.actions = [];
        // @state() cbMsg: string = "";
        // @state() header: string = "";
        this.selected = [];
        this.unselected = [];
        this.onConfirm = async () => {
            console.log("onConfirm: this.dialogOpenData", this.dialogOpenData);
            const pubDefId = this.dialogOpenData.pubDefId;
            // const items = this.selected.map((item) => item.langId).join(",");
            const pubDef = this.dialogOpenData.pubDef;
            const updateTargetKey = this.dialogOpenData.updateTargetKey;
            // const items = this.selected;
            // const { lstLanguages, lstOutputFormats, lstPubContentDtos } = pubDef;
            // console.log("onConfirm: lstPubContentDtos", lstPubContentDtos);
            // console.log("onConfirm: lstLanguages", lstLanguages);
            // console.log("onConfirm: lstOutputFormats", lstOutputFormats);
            console.log("onConfirm: pubDef", pubDef);
            console.log("onConfirm: updateTargetKey", updateTargetKey);
            console.log("onConfirm: this.selected", this.selected);
            const body = {
                ...pubDef,
                pubDefId,
                [updateTargetKey]: this.selected,
                // pubLangs: lstLanguages,
                // pubContent: lstPubContentDtos,
                // pubOutputs: lstOutputFormats,
            };
            body[updateTargetKey] = this.selected;
            // if (this.dialogOpenData.updateTargetKey) {
            console.log("onConfirm: body", body);
            // }
            await wexlib.editPubDef(body);
            // await this.dialogOpenData.closeCallback(body);
            // await this.dialogOpenData.refreshCallback(pubDefId);
            return true;
        };
    }
    static get styles() {
        // * {
        //   box-sizing: border-box;
        // }
        return css `
      .italic {
        font-style: italic;
      }

      #dialog {
        width: min(90vw, 600px);
      }
      .dialog-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 1rem;
        height: min(50vh, 300px);
        color: var(--font-color);
      }
      .dialog-content * {
        color: var(--font-color);
      }
      .dialog-content > *:not(:last-child) {
        margin-right: 0.5rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
        max-height: calc(2.25rem * 8);
        height: 30vh;
      }
      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .select-column {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }
      .select-column > span {
        margin-bottom: 0.5rem;
      }

      .select-actions {
        display: flex;
        flex-direction: column;
      }
      .select-actions > *:not(:last-child) {
        margin-bottom: 0.5rem;
      }
      .select-actions > * {
        cursor: pointer;
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this.init(state);
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    init(state) {
        this.actions = state.menus.dialog_select_actions;
        this.dialogOpenData = state.wex_select_dialog_open;
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        this.dialogOpenData = state.wex_select_dialog_open;
        // this.headerText = this.dialogOpenData.headerText;
        if (!!this.dialogOpenData) {
            console.log("stateChange: this.dialogOpenData", this.dialogOpenData);
            this.selected = this.dialogOpenData.data.filter((item) => item.selected);
            this.unselected = this.dialogOpenData.data.filter((item) => !item.selected);
        }
        // console.log("stateChange: this.selected", this.selected);
        // console.log("stateChange: this.unselected", this.unselected);
    }
    handlePickListUpdated(e) {
        console.log("handlePickListUpdated");
        this.selected = e.detail.selected;
        this.unselected = e.detail.unselected;
        console.log("handlePickListUpdated: this.selected", this.selected);
        console.log("handlePickListUpdated: this.unselected", this.unselected);
    }
    render() {
        // dialogOpenStateProperty=${this.dialogOpenData.dialogOpenStateProperty}
        const template = html `
      <wex-dialog
        id="dialog"
        .open=${!!this.dialogOpenData}
        .onConfirm=${this.onConfirm}
        confirmLabel="Ok"
        cancelLabel="Cancel"
        dialogOpenStateProperty="wex_select_dialog_open"
        headerText=${this.dialogOpenData?.headerText || "No Header"}
        width="60%"
      >
        <wex-list-picker
          .selected=${this.selected}
          .unselected=${this.unselected}
          @pick-list-updated=${this.handlePickListUpdated}
        ></wex-list-picker>
      </wex-dialog>
    `;
        return template;
    }
};
__decorate([
    property({ type: Object })
], WexDialogSelect.prototype, "dialogOpenData", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "state", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "string", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "subscription", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "actions", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "selected", void 0);
__decorate([
    state()
], WexDialogSelect.prototype, "unselected", void 0);
WexDialogSelect = __decorate([
    customElement("wex-dialog-select")
], WexDialogSelect);
export { WexDialogSelect };
//# sourceMappingURL=wex-select.js.map