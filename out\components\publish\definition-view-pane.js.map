{"version": 3, "file": "definition-view-pane.js", "sourceRoot": "", "sources": ["../../../src/components/publish/definition-view-pane.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAExC,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAGnB,IAAM,qBAAqB,GAA3B,MAAM,qBAAsB,SAAQ,UAAU;IAA9C;;QACuB,sBAAiB,GAAkB,IAAI,CAAC;QACxC,qBAAgB,GAAkB,IAAI,CAAC;QAE1D,WAAM,GAAQ,IAAI,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAEnB,UAAK,GAAQ,aAAa,CAAC,KAAK,CAAC;QACjC,WAAM,GAA2B,EAAE,CAAC;QACpC,iBAAY,GAAwB,IAAI,CAAC;IAuanD,CAAC;IAraC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2GT,CAAC;QACF,qBAAqB;QACrB,wCAAwC;QACxC,IAAI;IACN,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,oCAAoC;QACpC,mEAAmE;QAEnE,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAU;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,yDAAyD;QACzD,IAAI,KAAK,CAAC,sBAAsB,KAAK,IAAI,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CACrB,YAAoD;QAEpD,IAAI,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,IAAI,KAAI,CAAC;IAET,aAAa;QACnB,MAAM,OAAO,GAAG,IAAI,CAAA;;;;KAInB,CAAC;QACF,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO,OAAO,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAED,yCAAyC;IACjC,KAAK,CAAC,YAAY,CACxB,OAAsB,IAAI,EAC1B,IAAkB,IAAI;QAEtB,QAAQ;QACR,6BAA6B;QAC7B,iCAAiC;QACjC,iDAAiD;QACjD,+DAA+D;QAC/D,MAAM;QACN,yDAAyD;QACzD,0BAA0B;QAC1B,aAAa;IACf,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,iBAAiB;YAAE,OAAO;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC;QAC5C,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC;YACtE,IAAI,KAAK;gBAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC7B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,wCAAwC;gBAC7D,UAAU,EAAE,0BAA0B;gBACtC,eAAe,EAAE,YAAY;gBAC7B,IAAI,EAAE,GAAG;gBACT,wEAAwE;gBACxE,aAAa,EAAE,CAAC,SAAc,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC/D,eAAe,EAAE,KAAK,EAAE,EAAU,EAAE,EAAE;oBACpC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC3C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;gBACxB,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB;YAAE,OAAO;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QACnD,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,KAAK;gBAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC7B,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC;YACvB,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,UAAU,EAAE,gBAAgB;gBAC5B,eAAe,EAAE,UAAU;gBAC3B,IAAI,EAAE,GAAG;gBACT,2EAA2E;gBAC3E,aAAa,EAAE,CAAC,SAAc,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC/D,eAAe,EAAE,KAAK,EAAE,EAAU,EAAE,EAAE;oBACpC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC3C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;gBACxB,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;QAC3C,GAAG,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CACrB,CAAC,CAAM,EAAE,EAAE,CACT,CAAC,CAAC,eAAe,KAAK,CAAC,CAAC,eAAe;gBACvC,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,YAAY,CACpC,CAAC;YACF,IAAI,KAAK;gBAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC7B,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;gBAEnB,UAAU,EAAE,qBAAqB;gBACjC,eAAe,EAAE,YAAY;gBAC7B,IAAI,EAAE,GAAG;gBACT,aAAa,EAAE,CAAC,SAAc,EAAE,EAAE,CAChC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;gBAC1C,eAAe,EAAE,KAAK,EAAE,EAAU,EAAE,EAAE;oBACpC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC3C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;gBACxB,CAAC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA;;;;iBAIE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;kBACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAEzB,CAAC;IACnB,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAA;;;gBAGC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;8BACd,IAAI,CAAC,MAAM,EAAE,WAAW;;;gBAGtC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;;;qBAGtB,IAAI,CAAC,MAAM,EAAE,IAAI;uBACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC;;;;KAI7D,CAAC;IACJ,CAAC;IAEO,aAAa;QACnB,iEAAiE;QACjE,OAAO,IAAI,CAAA;;cAED,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;;;;;oBAKrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;;;UAIxC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC;YACzC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAChC,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAA,cAAc,CAAC,CAAC,IAAI,eAAe,CACpD;YACH,CAAC,CAAC,IAAI,CAAA;;wCAEwB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;aAElD;;KAER,CAAC;IACJ,CAAC;IAEO,eAAe;QACrB,+DAA+D;QAC/D,OAAO,IAAI,CAAA;cACD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;;;;;oBAKnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;;;UAItC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,CAC3B,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAA,cAAc,CAAC,CAAC,WAAW,eAAe,CAC3D;YACH,CAAC,CAAC,IAAI,CAAA;;wCAEwB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;aAElD;;QAEL,CAAC;IACP,CAAC;IAEO,aAAa;QACnB,mEAAmE;QACnE,OAAO,IAAI,CAAA;;cAED,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;;;;oBAKvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;;;UAIxC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,GAAG,CAC/B,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAA,cAAc,CAAC,CAAC,IAAI,eAAe,CACpD;YACH,CAAC,CAAC,IAAI,CAAA;;wCAEwB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;aAElD;;KAER,CAAC;IACJ,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM;YACd,OAAO,IAAI,CAAA;;kBAEC,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC;;OAErD,CAAC;QAEJ,MAAM,MAAM,GAAG,IAAI,CAAA;QACf,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE;QACpD,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;KACzE,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,oBAAoB;QAC1B,OAAO,IAAI,CAAA;4CAC6B,IAAI,CAAC,SAAS;UAChD,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;;KAEhD,CAAC;IACJ,CAAC;IAES,MAAM;QACd,OAAO,IAAI,CAAA;;;;;;UAML,IAAI,CAAC,oBAAoB,EAAE;;KAEhC,CAAC;IACJ,CAAC;CACF,CAAA;AA/a6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gEAAyC;AACxC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+DAAwC;AAE1D;IAAR,KAAK,EAAE;qDAAoB;AACnB;IAAR,KAAK,EAAE;wDAAmB;AALhB,qBAAqB;IADjC,aAAa,CAAC,0BAA0B,CAAC;GAC7B,qBAAqB,CAgbjC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\n\r\n@customElement(\"wex-definition-view-pane\")\r\nexport class WexDefinitionViewPane extends LitElement {\r\n  @property({ type: Number }) selectedProjectId: number | null = null;\r\n  @property({ type: Number }) selectedPubDefId: number | null = null;\r\n\r\n  @state() pubDef: any = null;\r\n  @state() isLoading = false;\r\n\r\n  private state: any = storeInstance.state;\r\n  private string: Record<string, string> = {};\r\n  private subscription: (() => void) | null = null;\r\n\r\n  static get styles() {\r\n    return css`\r\n      :host {\r\n        display: flex;\r\n        flex: 1;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        max-height: 70vh;\r\n      }\r\n\r\n      .loading {\r\n        position: absolute;\r\n        width: inherit;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        background-color: rgba(var(--clr-rgb-gray-ultra-light), 0.7);\r\n        z-index: 10;\r\n      }\r\n\r\n      .dev-view {\r\n        flex-direction: column;\r\n        width: 70%;\r\n        border-radius: 5px;\r\n        border: 1px solid #556b81;\r\n        padding: 1rem;\r\n        overflow: auto;\r\n        height: 100%;\r\n      }\r\n\r\n      .dev-view > *:not(:last-child) {\r\n        margin-bottom: 1rem;\r\n      }\r\n\r\n      ul {\r\n        flex-grow: 1;\r\n        margin: 0;\r\n        padding: 0;\r\n        list-style: none;\r\n        border: 1px solid var(--clr-gray-light);\r\n        overflow-y: auto;\r\n      }\r\n\r\n      li {\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 1rem;\r\n        height: 2.25rem;\r\n        border-bottom: 1px solid var(--clr-gray-light);\r\n      }\r\n      li > span {\r\n        flex-grow: 1;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n      li > .icon-container {\r\n        position: relative;\r\n        width: 1.5rem;\r\n        height: 1.5rem;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 8px;\r\n      }\r\n      li > .icon-container:hover {\r\n        background: var(--clr-white);\r\n      }\r\n      li > *:not(:last-child) {\r\n        margin-right: 1rem;\r\n      }\r\n      li[active] {\r\n        background: var(--row-selected-background);\r\n      }\r\n      li[active] {\r\n        background: var(--row-selected-background);\r\n      }\r\n      li:not([active]):hover {\r\n        background: var(--clr-gray-ultra-light);\r\n      }\r\n\r\n      .bold {\r\n        font-style: bold;\r\n      }\r\n      .italic {\r\n        font-style: italic;\r\n      }\r\n\r\n      .pointer {\r\n        cursor: pointer;\r\n      }\r\n\r\n      .action-icon,\r\n      .more-icon {\r\n        width: 1.5rem;\r\n        height: 1.5rem;\r\n        padding: 0.25rem;\r\n        border-radius: 0.25rem;\r\n      }\r\n      .action-icon:hover {\r\n        background-color: var(--clr-primary-ultra-light);\r\n      }\r\n\r\n      ui5-input {\r\n        width: 100%;\r\n      }\r\n    `;\r\n    // .more-icon:hover {\r\n    //   background-color: var(--clr-white);\r\n    // }\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    // this.state = storeInstance.state;\r\n    // this.string = storeInstance.state[storeInstance.state.langCode];\r\n\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this.init();\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  private async stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n\r\n    // refresh pubDef when dialog goes null ie) it was closed\r\n    if (state.wex_select_dialog_open === null) {\r\n      console.log(\"stateChange: wex_select_dialog_open is null\");\r\n      this.pubDef = await wexlib.getPubDef(this.selectedPubDefId);\r\n    }\r\n  }\r\n\r\n  protected async updated(\r\n    changedProps: Map<string | number | symbol, unknown>\r\n  ) {\r\n    if (changedProps.has(\"selectedPubDefId\")) {\r\n      if (this.selectedPubDefId) {\r\n        this.pubDef = await wexlib.getPubDef(this.selectedPubDefId);\r\n      } else {\r\n        this.pubDef = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  private init() {}\r\n\r\n  private renderLoading() {\r\n    const loading = html`\r\n      <div class=\"loading\">\r\n        <ui5-busy-indicator active> </ui5-busy-indicator>\r\n      </div>\r\n    `;\r\n    if (this.isLoading) return loading;\r\n    return null;\r\n  }\r\n\r\n  private closePubDef() {\r\n    this.selectedPubDefId = null;\r\n    this.pubDef = null;\r\n  }\r\n\r\n  // change handler for pubdef name // TODO\r\n  private async handleChange(\r\n    type: string | null = null,\r\n    e: Event | null = null\r\n  ) {\r\n    // try {\r\n    //   if (!type || !e) return;\r\n    //   if (type == \"pubdef-name\") {\r\n    //     const name = e.currentTarget?.value || \"\";\r\n    //     await wexlib.setPubDefName(this.pubDef?.pubDefId, name);\r\n    //   }\r\n    //   this.pubDef = await wexlib.getPubDef(item.pubDefId);\r\n    //   this.requestUpdate();\r\n    // } catch {}\r\n  }\r\n\r\n  private async editPubContent() {\r\n    console.log(\"editPubContent\");\r\n    if (!this.selectedProjectId) return;\r\n    const curr = this.pubDef?.lstPubContentDtos;\r\n    const res = await wexlib.getProjPubContents(this.selectedProjectId);\r\n    res.forEach((x: any) => {\r\n      const exist = curr.find((y: any) => y.pubContentId == x.pubContentId);\r\n      if (exist) x.selected = true;\r\n      return x;\r\n    });\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"wex_select_dialog_open\",\r\n      value: {\r\n        pubDefId: this.selectedPubDefId,\r\n        pubDef: this.pubDef, // pass entire pubDef instead of just ID\r\n        headerText: \"Edit Publication Content\",\r\n        updateTargetKey: \"pubContent\",\r\n        data: res,\r\n        // closeCallback: (closeData) => wexlib.setPubDefPubContents(closeData),\r\n        closeCallback: (closeData: any) => wexlib.editPubDef(closeData),\r\n        refreshCallback: async (id: number) => {\r\n          const updated = await wexlib.getPubDef(id);\r\n          this.pubDef = updated;\r\n        },\r\n      },\r\n    });\r\n  }\r\n\r\n  private async editPubLangs() {\r\n    console.log(\"editPubLangs\");\r\n    if (!this.selectedProjectId) return;\r\n    const curr = this.pubDef?.lstLanguages;\r\n    curr.forEach((x: any) => (x.name = x.description));\r\n    const res = await wexlib.getProjLanguages(this.selectedProjectId);\r\n    res.forEach((x: any) => {\r\n      const exist = curr.find((y: any) => y.langId == x.langId);\r\n      if (exist) x.selected = true;\r\n      x.name = x.description;\r\n      return x;\r\n    });\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"wex_select_dialog_open\",\r\n      value: {\r\n        pubDefId: this.selectedPubDefId,\r\n        pubDef: this.pubDef,\r\n        headerText: \"Edit Languages\",\r\n        updateTargetKey: \"pubLangs\",\r\n        data: res,\r\n        // closeCallback: (closeData: any) => wexlib.setPubDefLanguages(closeData),\r\n        closeCallback: (closeData: any) => wexlib.editPubDef(closeData),\r\n        refreshCallback: async (id: number) => {\r\n          const updated = await wexlib.getPubDef(id);\r\n          this.pubDef = updated;\r\n        },\r\n      },\r\n    });\r\n  }\r\n\r\n  private async editPubOutputs() {\r\n    const res = await wexlib.getPubEngineOutputs();\r\n    const curr = this.pubDef?.lstOutputFormats;\r\n    res.forEach((x: any) => {\r\n      const exist = curr.find(\r\n        (y: any) =>\r\n          y.publishEngineId === x.publishEngineId &&\r\n          y.outputTypeId === x.outputTypeId\r\n      );\r\n      if (exist) x.selected = true;\r\n      return x;\r\n    });\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"wex_select_dialog_open\",\r\n      value: {\r\n        pubDefId: this.selectedPubDefId,\r\n        pubDef: this.pubDef,\r\n\r\n        headerText: \"Edit Output Formats\",\r\n        updateTargetKey: \"pubOutputs\",\r\n        data: res,\r\n        closeCallback: (closeData: any) =>\r\n          wexlib.setPubDefEngineOutputs(closeData),\r\n        refreshCallback: async (id: number) => {\r\n          const updated = await wexlib.getPubDef(id);\r\n          this.pubDef = updated;\r\n        },\r\n      },\r\n    });\r\n  }\r\n\r\n  private renderCloseButton() {\r\n    return html` <wex-row-item justifyContent=\"right\">\r\n      <iron-icon\r\n        icon=\"clear\"\r\n        class=\"pointer action-icon\"\r\n        title=\"${this.string[\"_close\"]}\"\r\n        @click=\"${this.closePubDef.bind(this)}\"\r\n      ></iron-icon>\r\n    </wex-row-item>`;\r\n  }\r\n\r\n  private renderProjectName() {\r\n    return html`\r\n      <wex-row-item alignItems=\"flex-start\" height=\"7rem\">\r\n        <wex-col-item>\r\n          <h3>${this.string[\"_project_name\"]}</h3>\r\n          <ui5-input value=\"${this.pubDef?.projectName}\" readonly></ui5-input>\r\n        </wex-col-item>\r\n        <wex-col-item>\r\n          <h3>${this.string[\"_pubdef_name\"]}</h3>\r\n          <ui5-input\r\n            readonly\r\n            value=\"${this.pubDef?.name}\"\r\n            @change=\"${this.handleChange.bind(this, \"pubdef-name\")}\"\r\n          ></ui5-input>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  private renderContent() {\r\n    // title=\"${this.string[\"_edit_x\"](this.string[\"_pub_content\"])}\"\r\n    return html`\r\n      <wex-row-item justifyContent=\"space-between\">\r\n        <h3>${this.string[\"_pub_content\"]}</h3>\r\n        <iron-icon\r\n          title=\"Publication Content\"\r\n          icon=\"create\"\r\n          class=\"pointer action-icon\"\r\n          @click=\"${this.editPubContent.bind(this)}\"\r\n        ></iron-icon>\r\n      </wex-row-item>\r\n      <ul class=\"mainpane-list\">\r\n        ${this.pubDef?.lstPubContentDtos.length > 0\r\n          ? this.pubDef?.lstPubContentDtos.map(\r\n              (x: any) => html` <li><span>${x.name}</span></li> `\r\n            )\r\n          : html`\r\n              <li>\r\n                <span class=\"italic\"> ${this.string[\"_no_item\"]} </span>\r\n              </li>\r\n            `}\r\n      </ul>\r\n    `;\r\n  }\r\n\r\n  private renderLanguages() {\r\n    // title=\"${this.string[\"_edit_x\"](this.string[\"_languages\"])}\"\r\n    return html` <wex-row-item justifyContent=\"space-between\">\r\n        <h3>${this.string[\"_languages\"]}</h3>\r\n        <iron-icon\r\n          icon=\"create\"\r\n          class=\"pointer action-icon\"\r\n          title=\"Languages\"\r\n          @click=\"${this.editPubLangs.bind(this)}\"\r\n        ></iron-icon>\r\n      </wex-row-item>\r\n      <ul class=\"mainpane-list\">\r\n        ${this.pubDef?.lstLanguages.length > 0\r\n          ? this.pubDef?.lstLanguages.map(\r\n              (x: any) => html` <li><span>${x.description}</span></li> `\r\n            )\r\n          : html`\r\n              <li>\r\n                <span class=\"italic\"> ${this.string[\"_no_item\"]} </span>\r\n              </li>\r\n            `}\r\n      </ul>\r\n      .`;\r\n  }\r\n\r\n  private renderOutputs() {\r\n    // title=\"${this.string[\"_edit_x\"](this.string[\"_output_format\"])}\"\r\n    return html`\r\n      <wex-row-item justifyContent=\"space-between\">\r\n        <h3>${this.string[\"_output_format\"]}</h3>\r\n        <iron-icon\r\n          icon=\"create\"\r\n          class=\"pointer action-icon\"\r\n          title=\"Output Formats\"\r\n          @click=\"${this.editPubOutputs.bind(this)}\"\r\n        ></iron-icon>\r\n      </wex-row-item>\r\n      <ul class=\"mainpane-list\">\r\n        ${this.pubDef?.lstOutputFormats.length > 0\r\n          ? this.pubDef?.lstOutputFormats.map(\r\n              (x: any) => html` <li><span>${x.name}</span></li> `\r\n            )\r\n          : html`\r\n              <li>\r\n                <span class=\"italic\"> ${this.string[\"_no_item\"]} </span>\r\n              </li>\r\n            `}\r\n      </ul>\r\n    `;\r\n  }\r\n\r\n  private renderPubDef() {\r\n    if (!this.pubDef)\r\n      return html`\r\n        <wex-row-item justifyContent=\"center\">\r\n          <span>${this.string[\"_publish_select_pubdef_inst\"]}</span>\r\n        </wex-row-item>\r\n      `;\r\n\r\n    const pubDef = html`\r\n      ${this.renderCloseButton()} ${this.renderProjectName()}\r\n      ${this.renderContent()} ${this.renderLanguages()} ${this.renderOutputs()}\r\n    `;\r\n\r\n    return pubDef;\r\n  }\r\n\r\n  private renderDefinitionView() {\r\n    return html`\r\n      <section class=\"dev-view\" ?loading=\"${this.isLoading}\">\r\n        ${this.renderLoading()} ${this.renderPubDef()}\r\n      </section>\r\n    `;\r\n  }\r\n\r\n  protected render() {\r\n    return html`\r\n      <wex-col-item\r\n        justifyContent=\"flex-start\"\r\n        alignItems=\"center\"\r\n        height=\"500px\"\r\n      >\r\n        ${this.renderDefinitionView()}\r\n      </wex-col-item>\r\n    `;\r\n  }\r\n}\r\n"]}