{"version": 3, "file": "wex-ditamap-tree_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/wex-ditamap-tree_test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,mCAAmC,CAAC;AAG3C,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC;IAExC,MAAM,CAAC,KAAK,IAAI,EAAE;QAChB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,GAAG,EAAE;QACT,MAAM,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,EAAE,GAAmB,MAAM,OAAO,CAAC,IAAI,CAAA,4FAA4F,CAAC,CAAC;QAE3I,4BAA4B;QAC5B,MAAM,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,SAAS,EAAE,mBAAmB,CAAC,CAAC;QACpE,IAAI,MAAM,GAAG,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,IAAI,GAAG,IAAI,cAAc,CAAC,MAAqB,CAAC,CAAC;QACvD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAClC,4CAA4C,CAC7C,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAC3C,EAAE,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACtB,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,KAAK,GAAG,KAAK,EAAE,IAAY,EAAE,EAAE;IAC1C,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC", "sourcesContent": ["import { setupWorker } from \"msw/browser\";\r\nimport { handlers } from \"./msw/handlers\";\r\nimport { expect, fixture, html } from \"@open-wc/testing\";\r\nimport { waitUntil } from \"@open-wc/testing-helpers\";\r\nimport { TreeTestDriver } from \"./drivers/bds-tree-driver\";\r\nimport \"../../components/wex-ditamap-tree\";\r\nimport { WexDitamapTree } from \"../../components/wex-ditamap-tree\";\r\n\r\ndescribe(\"Tree rendering\", () => {\r\n  const worker = setupWorker(...handlers);\r\n\r\n  before(async () => {\r\n    await worker.start();\r\n  });\r\n\r\n  after(() => {\r\n    worker.stop();\r\n  });\r\n\r\n  it(\"shows the tree when loading succeeds\", async () => {\r\n    const el: WexDitamapTree = await fixture(html`<wex-ditamap-tree rootMap=\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"></wex-ditamap-tree>`);\r\n\r\n    // Wait for the tree to load\r\n    await waitUntil(() => el.status === \"success\", \"Did not load tree\");\r\n    let treeEl = el.shadowRoot?.querySelector(\"bds-tree\");\r\n    const tree = new TreeTestDriver(treeEl as HTMLElement);\r\n    const node = await tree.getNodeByKey(\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n    );\r\n    expect(node.label).to.equal(\"Phone 1 User Guide\");\r\n  });\r\n\r\n  it.skip(\"shows a loading state\", () => {});\r\n  it.skip(\"shows an error when loading fails\");\r\n});\r\n\r\ndescribe(\"Delete\", () => {\r\n  it.skip(\"deletes a node\", () => {});\r\n});\r\n\r\nexport const sleep = async (time: number) => {\r\n  await new Promise((resolve) => setTimeout(resolve, time));\r\n};\r\n"]}