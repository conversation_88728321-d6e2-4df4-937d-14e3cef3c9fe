{"version": 3, "file": "definitions-action-bar.js", "sourceRoot": "", "sources": ["../../../src/components/publish/definitions-action-bar.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AACxC,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AAEpC,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,cAAc,CAAC;AAGf,IAAM,8BAA8B,GAApC,MAAM,8BAA+B,SAAQ,UAAU;IAAvD;;QACsB,aAAQ,GAAU,EAAE,CAAC;QACrB,YAAO,GAAU,EAAE,CAAC;QAEtC,YAAO,GAAQ,IAAI,CAAC;QA6D7B,yBAAoB,GAAG,GAAG,EAAE;YAC1B,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,wBAAwB,EAAE;gBACxC,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC;QAEF,6BAAwB,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,4BAA4B,EAAE;gBAC5C,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC;IAiCJ,CAAC;IA5GC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAC5B,CAAC,QAAQ,CAAC,CAAC,8CAA8C;QAC1D,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAChE,sFAAsF;QACtF,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC9C,kDAAkD;QAClD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,iCAAiC;YAC3C,KAAK,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,cAAc;aACvB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClE,+CAA+C;YAE/C,4DAA4D;YAE5D,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACvE,CAAC;QAAC,MAAM,CAAC;QACT,CAAC;gBAAS,CAAC;QACX,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,gEAAgE;QAChE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YACtC,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE5C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;YAC7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;gBACzC,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE;oBACnC,OAAO;iBACR,CAAC,CAAC;gBACH,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB;YACxD,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAoBD,MAAM;QACJ,OAAO,IAAI,CAAA;;;;;;;;wBAQS,IAAI,CAAC,wBAAwB;;kCAEnB,IAAI,CAAC,oBAAoB;;;;;;;;iCAQ1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;kCAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;wBAEpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;KAOhD,CAAC;IACJ,CAAC;CACF,CAAA;AAjH4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gEAAsB;AACrB;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;+DAAqB;AAEtC;IAAR,KAAK,EAAE;+DAAqB;AAJlB,8BAA8B;IAD1C,aAAa,CAAC,oCAAoC,CAAC;GACvC,8BAA8B,CAkH1C", "sourcesContent": ["import { LitElement, html, css } from \"lit-element\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib.js\";\r\nimport * as util from \"lib/util.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\nimport \"../base/icon\";\r\n\r\n@customElement(\"wex-publish-definitions-action-bar\")\r\nexport class WexPublishDefinitionsActionBar extends LitElement {\r\n  @property({ type: Array }) projects: any[] = [];\r\n  @property({ type: Array }) pubDefs: any[] = [];\r\n\r\n  @state() _string: any = null;\r\n\r\n  static get styles() {\r\n    return css``;\r\n  }\r\n\r\n  async _editPubDef() {\r\n    console.log(\"***********************\");\r\n    const selectedPubDefId = this.pubDefs.find(\r\n      (pubDef) => pubDef.selected\r\n    ).pubDefId; // need to get from API to ensure correct data\r\n    const selectedPubDef = await wexlib.getPubDef(selectedPubDefId);\r\n    // need to toast if multiples are selected; it only supports a single pubdef selection\r\n    console.log(\"selectedPubDef Array:\", selectedPubDef);\r\n    console.log(\"pubDef to edit\", selectedPubDef);\r\n    // console.log(\"fresh data:\", selectedPubDefTest);\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"publish_defs_create_dialog_open\",\r\n      value: {\r\n        mode: \"Edit\",\r\n        projects: this.projects,\r\n        pubDef: selectedPubDef,\r\n      },\r\n    });\r\n  }\r\n\r\n  async _runPubDef() {\r\n    try {\r\n      const selected = this.pubDefs.filter((pubDef) => pubDef.selected);\r\n      // bail out if anything is not totally complete\r\n\r\n      // TODO Xdocs issue with sql tables adding unremovable items\r\n\r\n      await wexlib.runPubDefs(selected);\r\n      util.notifyUser(\"positive\", this._string[\"_msg_pubish_run_success\"]);\r\n    } catch {\r\n    } finally {\r\n    }\r\n  }\r\n\r\n  async _deletePubDef() {\r\n    // console.log(\"delete pubdef with id\", this._selectedPubDefId);\r\n    try {\r\n      const selected = this.pubDefs.filter((pubDef) => pubDef.selected);\r\n      console.log(\"delete this:\", selected);\r\n      await wexlib.rmPubDef(selected[0].pubDefId);\r\n\r\n      const pubDefs = await wexlib.getAllPubDefs();\r\n      console.log(\"pubDefs after delete\", pubDefs);\r\n      if (pubDefs.length) {\r\n        console.log(\"settings pubdefs\", pubDefs);\r\n        storeInstance.dispatch(\"setPubDefs\", {\r\n          pubDefs,\r\n        });\r\n        storeInstance.dispatch(\"touch\"); // force state update\r\n      }\r\n    } catch (e) {\r\n      console.error(\"delete\", e);\r\n    }\r\n  }\r\n\r\n  _handleSelectAllRows = () => {\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"select-all-definitions\", {\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n  };\r\n\r\n  _handleClearSelectedRows = () => {\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"clear-selected-definitions\", {\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n  };\r\n\r\n  render() {\r\n    return html`\r\n      <wex-row-item height=\"42px\">\r\n        <wex-col-item>\r\n          <wex-row-item justifyContent=\"flex-start\" alignItems=\"center\">\r\n            <wex-icon\r\n              icon=\"icons:clear\"\r\n              class=\"pointer action-icon\"\r\n              title=\"Clear selection\"\r\n              @click=\"${this._handleClearSelectedRows}\"\r\n            ></wex-icon>\r\n            <ui5-button @click=\"${this._handleSelectAllRows}\"\r\n              >Select all</ui5-button\r\n            >\r\n          </wex-row-item>\r\n        </wex-col-item>\r\n\r\n        <wex-col-item>\r\n          <wex-row-item justifyContent=\"flex-end\" alignItems=\"center\">\r\n            <ui5-button @click=${this._editPubDef.bind(this)}>Edit</ui5-button>\r\n            <ui5-button @click=\"${this._runPubDef.bind(this)}\">Run</ui5-button>\r\n            <ui5-button\r\n              @click=\"${this._deletePubDef.bind(this)}\"\r\n              design=\"Negative\"\r\n              >Delete</ui5-button\r\n            >\r\n          </wex-row-item>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n}\r\n"]}