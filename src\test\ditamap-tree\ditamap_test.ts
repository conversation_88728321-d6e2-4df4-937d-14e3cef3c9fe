/**
 * The ditamap editor involves many parts
 *  1. Storing a hashmap of the files
 *  2. Building a JSON tree from the xml
 *  3. Displaying the tree
 *  4. Editing the tree
 *  5. Saving the tree
 *  6. Validating the tree
 */
import {
  DitamapNode,
  MapRefLikeElementNode,
  RootMapNode,
} from "../../lib/jsDitamap/ditamap-node";
import { DitamapTree } from "../../lib/jsDitamap/ditamap-tree";
import { DitamapUtils } from "../../lib/jsDitamap/ditamap-utils";
import { PHONES_TREE } from "./data/json-trees";
import { PHONES_1_BOOKMAP, SUBMAP_PHONES } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";

describe("Building JSON tree from xml", () => {
  it("builds phones tree", () => {
    let bookmap = new DOMParser().parseFromString(
      PHONES_1_BOOKMAP,
      "application/xml"
    ).documentElement;
    let submap = new DOMParser().parseFromString(
      SUBMAP_PHONES,
      "application/xml"
    ).documentElement;
    let workspace = new Map<string, HTMLElement>();
    workspace.set("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", bookmap);
    workspace.set("/Content/submap_phones_xi1609_1_1.ditamap", submap);

    let tree = new DitamapTree(
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      workspace
    );

    expect(pruneTree(PHONES_TREE, ["href", "children"])).to.deep.equal(
      pruneTree(tree.root, [
        "href",
        "children",
        // "containingMap",
      ])
    );
  });
  it.skip("builds a tree from a simple bookmap");
});

describe("Is Map reference", () => {
  it("part with href and format=ditamap => true", () => {
    let document = new DOMParser().parseFromString(
      `<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />`,
      "application/xml"
    );
    expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;
  });

  it("mapref with href and format=ditamap => true", () => {
    let document = new DOMParser().parseFromString(
      `<mapref format="ditamap" href="/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap"
        class="+ map/topicref mapgroup-d/mapref " />`,
      "application/xml"
    );
    expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;
  });
});

describe("Is TopicRefLike", () => {
  it("topicref => true", () => {
    let document = new DOMParser().parseFromString(
      `<topicref href="/Content/begin_xi1612_1_1.xml" class="- map/topicref " />`,
      "application/xml"
    );
    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
  });

  it("chapter => true", () => {
    let document = new DOMParser().parseFromString(
      `<chapter href="/Content/Introduction_xi1674_1_1.xml" class="- map/topicref bookmap/chapter">`,
      "application/xml"
    );
    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
  });

  it("topichead => true", () => {
    let document = new DOMParser().parseFromString(
      `<topichead href="/Content/Introduction_xi1674_1_1.xml" class="- map/topicref bookmap/chapter">`,
      "application/xml"
    );
    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;
  });

  it("bookmap => false", () => {
    let document = new DOMParser().parseFromString(
      `<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
        id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
        class="- map/map bookmap/bookmap ">`,
      "application/xml"
    );
    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;
  });

  it("part => false", () => {
    let document = new DOMParser().parseFromString(
      `<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />`,
      "application/xml"
    );
    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;
  });
});

describe("Get map name (href)", () => {
  it("If the element is a bookmap, use the rootMapName", () => {
    let document = new DOMParser().parseFromString(
      `<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
        id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
        class="- map/map bookmap/bookmap ">`,
      "application/xml"
    );
    expect(
      DitamapUtils.getMapName(
        document.documentElement,
        "/Content/Phone1_Bookmap_xi1577_1_1.ditamap"
      )
    ).to.equal("/Content/Phone1_Bookmap_xi1577_1_1.ditamap");
  });
});
describe("Root Map Node", () => {
  it("should create node from bookmap", () => {
    let element = new DOMParser().parseFromString(
      `
        <bookmap 
        class="- map/map bookmap/bookmap ">
        	<booktitle class="- topic/title bookmap/booktitle ">
		<mainbooktitle class="- topic/ph bookmap/mainbooktitle ">
			<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>
	</booktitle>
        </bookmap>
          `,
      "application/xml"
    ).documentElement;
    let node = new RootMapNode(
      element,
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap"
    );
;
    expect(node).to.deep.equal({
      href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      containingMap: null,
      title: "Phone 1 User Guide",
      refElement: null,
      structuralElement: element,
      mapPath: [],
      children: [],
    });
  });
});
describe("Map Ref Like Node", () => {
  it("should create node from part", () => {
    let element = new DOMParser().parseFromString(
      `
      <part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />
    `,
      "application/xml"
    ).documentElement;
    let structuralElement = new DOMParser().parseFromString(
      `
        <map title="Submap Phones"
    class="- map/map "></map>
      `,
      "application/xml"
    ).documentElement;
    let node = new MapRefLikeElementNode(
      element,
      structuralElement,
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap"
    );
    expect(node).to.deep.equal({
      href: "/Content/submap_phones_xi1609_1_1.ditamap",
      containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      title: "Submap Phones",
      refElement: element,
      structuralElement: structuralElement,
      mapPath: [],
      children: [],
    });
  });
});

const pruneTree = <T extends DitamapNode>(
  current: DitamapNode | null,
  fields: (keyof T)[]
) => {
  if (!current) return null;

  let pruned: T = {} as T;

  fields.forEach((field) => {
    if (current[field] != undefined) pruned[field] = current[field];
  });

  pruned.children = current.children.map((child) => pruneTree(child, fields));
  return pruned;
};
