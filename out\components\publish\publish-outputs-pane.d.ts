import { LitElement } from "lit";
import "../base/row-item";
import "../base/col-item";
import "../wex-table";
import "@ui5/webcomponents/dist/Button.js";
export declare class WexPublishOutputsPane extends LitElement {
    outputJobs: any[];
    private string;
    private outputJobsColumns;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    _openPopover(id: string): void;
    _renderOutputJobActionBar(): import("lit").TemplateResult<1>;
    _handleClickOutputJobRow(e: CustomEvent): Promise<void>;
    _handleDownloadClick(): Promise<void>;
    render(): import("lit").TemplateResult<1>;
}
