import { LitElement } from "lit";
import "@ui5/webcomponents-compat/dist/Table.js";
import "@ui5/webcomponents-compat/dist/TableRow.js";
import "@ui5/webcomponents-compat/dist/TableCell.js";
import "@ui5/webcomponents-compat/dist/TableColumn.js";
import "@vaadin/vaadin-context-menu/vaadin-context-menu.js";
import "@vaadin/vaadin-list-box/vaadin-list-box.js";
import "@vaadin/vaadin-item/vaadin-item.js";
import "@ui5/webcomponents/dist/RadioButton";
import { Task } from "@bds/types";
import { FileMeta } from "@bds/types";
export declare class WexTaskFilepackage extends LitElement {
    store: any;
    subscription: any;
    state: any;
    string: Record<string, string>;
    primaryRadioBtn: Element | null;
    contextMenuItems: MenuItem[];
    isPrimarySelected: boolean;
    contextMenu: Element | null;
    /**
     * Currently selected workflow tab
     */
    view: "task" | "process";
    /**
     * Task selected in the active tasks table
     */
    selectedTask: Task | null;
    /**
     * author: 4,  contributor: 3,  review: 2,  default: 1
     */
    effectiveCap: string;
    claimedTasks: Task[];
    columns: Column[];
    rows: FileMeta[];
    fileList: FileMeta[];
    selectedFile: FileMeta | null;
    connectedCallback(): void;
    disconnectedCallback(): void;
    updated(changedProperties: Map<string, any>): void;
    firstUpdated(): void;
    stateChange(state: any): void;
    _marshellColumns(): void;
    _marshellRows(): void;
    _isClaimed(): boolean;
    _addFileButton(): import("lit").TemplateResult<1> | undefined;
    _importFileButton(): import("lit").TemplateResult<1> | undefined;
    _createFileButton(): import("lit").TemplateResult<1> | undefined;
    _colHeading(col: any): any;
    render(): import("lit").TemplateResult<1>;
    _sortColumn(e: any): void;
    _rowTemplate(col: any, itm: any): import("lit").TemplateResult<1>;
    _toggleReviewed(e: any): void;
    _toggleReviewedAction(selectedTask: Task, selectedFile: FileMeta): void;
    _fileRoleChange(): void;
    addFileCallBack(addFile: any): void;
    createFileCallBack(): void;
    _filePackageAction(e: any): void;
    _defaultAction(e: any): void;
    _contextmenuclicked(e: any): void;
    _editAction(): void;
    _handleRowRightClicked(e: any): void;
    _handleRowClicked(e: any): void;
    _getProjectFromCurrentTask(): any;
    _getrFolderFromCurrentTask(): any;
    _folderById(node: any, collId: any, c: any): any;
    _findFolderTest(root: any, collId: any): any;
}
interface Column {
    title: string;
    fieldname: string;
    order: number;
    sort: "asc" | "desc" | "" | "muted";
    heading: string;
}
interface MenuItem {
    name: string;
    label: string;
}
export {};
