{"version": 3, "file": "wizard-stage-tabs.js", "sourceRoot": "", "sources": ["../../../src/components/common/wizard-stage-tabs.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAG5D,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;IAA3C;;QACuB,WAAM,GAAwB,EAAE,CAAC;QACpD,UAAK,GAAW,OAAO,CAAC;IA4DnC,CAAC;IA1DC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgCT,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9C,OAAO,IAAI,CAAA,2BAA2B,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK;;;mBAGlD,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;sBAC5B,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI;;;YAGlC,KAAK,CAAC,KAAK;;YAEX,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAES,MAAM;QACd,OAAO,IAAI,CAAA;;UAEL,IAAI,CAAC,iBAAiB,EAAE;;KAE7B,CAAC;IACJ,CAAC;CACF,CAAA;AA7D6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kDAAkC;AACpD;IAAR,KAAK,EAAE;iDAAyB;AAFtB,kBAAkB;IAD9B,aAAa,CAAC,uBAAuB,CAAC;GAC1B,kBAAkB,CA8D9B", "sourcesContent": ["import { LitElement, html, css } from \"lit-element\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n\r\n@customElement(\"wex-wizard-stage-tabs\")\r\nexport class WexWizardStageTabs extends LitElement {\r\n  @property({ type: Object }) stages: Record<string, any> = {};\r\n  @state() stage: string = \"basic\";\r\n\r\n  static get styles() {\r\n    return css`\r\n      .tab {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--spacing-sm) 0;\r\n        cursor: pointer;\r\n        width: fit-content;\r\n        color: var(--color-text);\r\n      }\r\n      .tab[active] {\r\n        border-bottom: 2px solid;\r\n        font-weight: bold;\r\n      }\r\n      .tab:not([active]):hover {\r\n        background-color: var(--clr-white);\r\n        color: var(--primary);\r\n      }\r\n      .tab-btn:disabled {\r\n        pointer-events: none;\r\n        color: #b1b0b0;\r\n      }\r\n\r\n      .stage-tabs {\r\n        padding: 0;\r\n        margin: 0;\r\n        width: 10rem;\r\n      }\r\n\r\n      .tab-btn {\r\n        background: none;\r\n        border: none;\r\n      }\r\n    `;\r\n  }\r\n\r\n  private renderStageTitles() {\r\n    return Object.values(this.stages).map((stage) => {\r\n      return html`<li class=\"tab\" ?active=${stage.name === this.stage}>\r\n        <button\r\n          class=\"tab-btn\"\r\n          @click=${() => (this.stage = stage.name)}\r\n          ?disabled=${this.stage != stage.name}\r\n          role=\"tab\"\r\n        >\r\n          ${stage.title}\r\n        </button>\r\n      </li>`;\r\n    });\r\n  }\r\n\r\n  protected render() {\r\n    return html`\r\n      <ul class=\"stage-tabs\">\r\n        ${this.renderStageTitles()}\r\n      </ul>\r\n    `;\r\n  }\r\n}\r\n"]}