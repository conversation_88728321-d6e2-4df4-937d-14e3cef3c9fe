{"version": 3, "file": "ditamap-tree.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EAEX,qBAAqB,EACrB,WAAW,GACZ,MAAM,gBAAgB,CAAC;AAExB,MAAM,OAAO,WAAW;IActB,YAAY,WAAmB,EAAE,KAAgC;QATjE;;WAEG;QACH,SAAI,GAAuB,IAAI,CAAC;QAO9B,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,SAAS,CAAC,OAAe;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,QAAqB;QAC5B,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEvD,IAAI,CAAC,GAAgB,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAe,CAAC;YAExD,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAyC,EAAE,CAAC;gBACpE,IAAI,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBACpC,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACnC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAW,CACrC,CAAC;oBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;oBACzD,CAAC;oBAED,YAAY,GAAG,YAAY,IAAI,KAAK,CAAC;oBAErC,IAAI,iBAAiB,GAAG,IAAI,qBAAqB,CAC/C,KAAK,EACL,YAAY,EACZ,OAAO,CACR,CAAC;oBAEF,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBACjE,IAAI,YAAY,GAAG,UAAU,IAAI,OAAO,CAAC;oBAEzC,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBAC/C,CAAC;oBAED,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC;oBACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC1C,CAAC;gBAED,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,IAAI,WAAW,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAClD,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB;IACnB;;;OAGG;IACH,MAAM,CAAC,YAAY,CAAC,OAAoB;QACtC,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC;QACrE,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,OAAoB;QACnC,OAAO,OAAO,CAAC,OAAO,KAAK,UAAU,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,UAAU,CACf,OAAoB,EACpB,WAAoB;QAEpB,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAW,CAAC;QAChD,CAAC;QAED,IACE,WAAW;YACX,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,SAAS;gBAC1C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAC1C,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAoB;QACrC,IAAI,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC7D,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC,SAAS,CAAC;QAEtD,IAAI,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;QAE3D,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAE9B,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,IAAI,GAAG;YAAE,OAAO,GAAG,CAAC,OAAO,CAAC,sCAAsC,EAAE,EAAE,CAAC,CAAC;QACxE,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAID,uCAAuC;AACvC,WAAW;AACX,eAAe;AACf,iBAAiB;AACjB,kBAAkB;AAClB,MAAM;AAEN,mEAAmE;AAEnE,wEAAwE;AAExE;;;GAGG", "sourcesContent": ["import {\r\n  ContentNode,\r\n  DitamapNode,\r\n  MapRefLikeElementNode,\r\n  RootMapNode,\r\n} from \"./ditamap-node\";\r\n\r\nexport class DitamapTree {\r\n  /**\r\n   * Map of all the stored ditamap xml\r\n   */\r\n  workspace: Map<string, HTMLElement>;\r\n  /**\r\n   * Underlying tree data structure\r\n   */\r\n  root: DitamapNode | null = null;\r\n  /**\r\n   * Name of the root map\r\n   */\r\n  rootMapName: string;\r\n\r\n  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {\r\n    this.workspace = files ?? new Map();\r\n    this.root = this.buildTree(rootMapName);\r\n    this.rootMapName = rootMapName;\r\n  }\r\n\r\n  buildTree(mapName: string): DitamapNode | null {\r\n    if (this.workspace.has(mapName)) {\r\n      return this._treeBfs(this.workspace.get(mapName)!);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Builds json tree from xml\r\n   * @param rootHtml - parsed xml element\r\n   */\r\n  _treeBfs(rootHtml: HTMLElement): DitamapNode {\r\n    let tree = new RootMapNode(rootHtml, this.rootMapName);\r\n\r\n    let Q: QueueItem[] = [[rootHtml, tree, this.rootMapName]];\r\n    while (Array.isArray(Q) && Q.length > 0) {\r\n      let [element, parent, mapName] = Q.shift() as QueueItem;\r\n\r\n      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {\r\n        if (DitamapTree.isMapRefLike(child)) {\r\n          let structuralEl = this.workspace.get(\r\n            child.getAttribute(\"href\") as string\r\n          );\r\n\r\n          if (!structuralEl) {\r\n            console.warn(\"No structural element found for\", child);\r\n          }\r\n\r\n          structuralEl = structuralEl ?? child;\r\n\r\n          let newMapRefLikeNode = new MapRefLikeElementNode(\r\n            child,\r\n            structuralEl,\r\n            mapName\r\n          );\r\n\r\n          let newMapName = DitamapTree.getMapName(child, this.rootMapName);\r\n          let mapNameToUse = newMapName ?? mapName;\r\n\r\n          if (!newMapName) {\r\n            console.warn(\"No map name found for\", child);\r\n          }\r\n\r\n          Q.push([structuralEl, newMapRefLikeNode, mapNameToUse]);\r\n          parent.children.push(newMapRefLikeNode);\r\n        }\r\n\r\n        if (DitamapTree.isContent(child)) {\r\n          let contentNode = new ContentNode(child, mapName);\r\n          Q.push([child, contentNode, mapName]);\r\n          parent.children.push(contentNode);\r\n        }\r\n      }\r\n    }\r\n\r\n    return tree;\r\n  }\r\n\r\n  //---- Helpers ----\r\n  /**\r\n   * Checks if an html element is a mapref or part\r\n   * @param element\r\n   */\r\n  static isMapRefLike(element: HTMLElement): boolean {\r\n    const hasDitamapFormat = element.getAttribute(\"format\") == \"ditamap\";\r\n    const hasHref = element.getAttribute(\"href\");\r\n    return Boolean(hasDitamapFormat && hasHref);\r\n  }\r\n\r\n  /**\r\n   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)\r\n   * @param element\r\n   */\r\n  static isContent(element: HTMLElement): boolean {\r\n    return element.tagName === \"topicref\" || element.tagName === \"chapter\";\r\n  }\r\n\r\n  /**\r\n   * Gets the href used to identify the map, if no href assume it is the root map\r\n   * @param element - xml element\r\n   * @param rootMapName - name of the root map\r\n   */\r\n  static getMapName(\r\n    element: HTMLElement,\r\n    rootMapName?: string\r\n  ): string | undefined {\r\n    if (element.getAttribute(\"href\")) {\r\n      return element.getAttribute(\"href\") as string;\r\n    }\r\n\r\n    if (\r\n      rootMapName &&\r\n      (element.tagName.toLowerCase() === \"bookmap\" ||\r\n        element.tagName.toLowerCase() === \"map\")\r\n    ) {\r\n      return rootMapName;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  static getMapTitle(element: HTMLElement) {\r\n    let mainbooktitleEl = element.querySelector(\"mainbooktitle\");\r\n    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;\r\n\r\n    let titleEl = element.querySelector(\"title\");\r\n    if (titleEl) return titleEl.innerText || titleEl.innerHTML;\r\n\r\n    let titleAtt = element.getAttribute(\"title\");\r\n    if (titleAtt) return titleAtt;\r\n\r\n    return element.tagName;\r\n  }\r\n\r\n  static removeXmlNoise(str: string) {\r\n    if (str) return str.replace(\"<?xm-replace_text Main Book Title ?>\", \"\");\r\n    return str;\r\n  }\r\n}\r\n\r\ntype QueueItem = [HTMLElement, DitamapNode, string];\r\n\r\n// const structuralElements = new Set([\r\n//   \"map\",\r\n//   \"bookmap\",\r\n//   \"topichead\",\r\n//   \"topicgroup\",\r\n// ]);\r\n\r\n// const referenceElements = new Set([\"mapref\", \"part\", \"keydef\"]);\r\n\r\n// const dualUseElements = new Set([\"topicref\", \"chapter\", \"appendix\"]);\r\n\r\n/**\r\n *\r\n * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#\r\n */\r\n"]}