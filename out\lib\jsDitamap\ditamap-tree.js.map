{"version": 3, "file": "ditamap-tree.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AACA,OAAO,EACL,kBAAkB,EAElB,gBAAgB,EAChB,WAAW,EACX,cAAc,GACf,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,MAAM,OAAO,WAAW;IActB,YAAY,WAAmB,EAAE,KAAgC;QATjE;;WAEG;QACH,SAAI,GAAuB,IAAI,CAAC;QAO9B,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,KAAK,IAAI,IAAI,GAAG,EAAE;YAC7B,QAAQ,EAAE,IAAI,GAAG,EAAE;YACnB,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,IAAI;SACZ,CAAC;QACF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnD,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAe;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,QAAQ,CAAC,QAAqB;QAClC,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC;YACzB,WAAW,EAAE,QAAQ;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,GAAgB,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAe,CAAC;YAExD,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAyC,EAAE,CAAC;gBACpE,QAAQ,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAChD,KAAK,kBAAkB,CAAC,CAAC,CAAC;wBACxB,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACjD,KAAK,CAAC,YAAY,CAAC,MAAM,CAAE,CAC5B,CAAC;wBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;4BAClB,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;wBACzD,CAAC;wBAED,YAAY,GAAG,YAAY,IAAI,KAAK,CAAC;wBAErC,IAAI,mBAAmB,GAAG,IAAI,gBAAgB,CAAC;4BAC7C,UAAU,EAAE,KAAK;4BACjB,iBAAiB,EAAE,YAAY;4BAC/B,aAAa,EAAE,OAAO;4BACtB,OAAO,EAAE,IAAI,CAAC,OAAO;yBACtB,CAAC,CAAC;wBAEH,IAAI,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;wBAClE,IAAI,YAAY,GAAG,UAAU,IAAI,OAAO,CAAC;wBAEzC,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;wBAC/C,CAAC;wBAED,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAC,CAAC;wBAC1D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAC1C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;wBACrC,SAAS;oBACX,CAAC;oBAED,KAAK,gBAAgB,CAAC,CAAC,CAAC;wBACtB,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;wBACxD,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;wBACzC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;wBACrC,SAAS;oBACX,CAAC;oBAED,KAAK,oBAAoB,CAAC,CAAC,CAAC;wBAC1B,IAAI,WAAW,GAAG,IAAI,kBAAkB,CAAC;4BACvC,UAAU,EAAE,KAAK;4BACjB,aAAa,EAAE,OAAO;4BACtB,OAAO,EAAE,IAAI,CAAC,OAAO;yBACtB,CAAC,CAAC;wBACH,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAClC,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;4BAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC,CAAC;wBACtD,CAAC;wBACD,SAAS;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB,CAAC,IAAY;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,mEAAmE;IACnE,YAAY,CAAC,MAAmB;QAC9B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,EAAiB,CAAC;YAElC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChC,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;YACvC,CAAC;YAED,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,QAAyC,EAAE,CAAC;gBAC/D,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACtC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAE3B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,wBAAwB;IAExB,KAAK,CAAC,cAAc,CAAC,SAAiB;QACpC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,iDAAiD,SAAS,EAAE,CAC7D,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,EAAE,CAAC;YAChB,CAAC;YACD,MAAM,IAAI,GAAe,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/C,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,KAAK,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC7B,IAAI,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB;YACzD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;YAE1D,eAAe;YACf,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CACzB,wBAAwB,EACxB,GAAG,CAAC,eAAe,EACnB,IAAI,EACJ,WAAW,CAAC,0BAA0B,EACtC,IAAI,CACL,CAAC;YAEF,IAAI,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACrC,OAAO,OAAO,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;oBACjC,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;gBACrD,CAAC;gBACD,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;CAGF;AAYD;;;GAGG", "sourcesContent": ["import { FileMeta } from \"@bds/types\";\r\nimport {\r\n  TopicReferenceNode,\r\n  DitamapNode,\r\n  MapReferenceNode,\r\n  RootMapNode,\r\n  StructuralNode,\r\n} from \"./ditamap-node\";\r\nimport { DitamapUtils } from \"./ditamap-utils\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\n\r\nexport class DitamapTree {\r\n  /**\r\n   * Context containing workspace, metadata, and other shared state\r\n   */\r\n  context: DitamapContext;\r\n  /**\r\n   * Underlying tree data structure\r\n   */\r\n  root: DitamapNode | null = null;\r\n  /**\r\n   * Name of the root map\r\n   */\r\n  rootMapName: string;\r\n\r\n  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {\r\n    this.context = {\r\n      workspace: files ?? new Map(),\r\n      metadata: new Map(),\r\n      dirty: new Set(),\r\n      buffer: null,\r\n      cutId: null,\r\n    };\r\n    this.rootMapName = rootMapName;\r\n  }\r\n\r\n  getRoot() {\r\n    return this.root;\r\n  }\r\n\r\n  async load() {\r\n    await this._fetchMap(this.rootMapName);\r\n    this.root = await this.buildTree(this.rootMapName);\r\n    await this._fetchMetadata([...this.context.metadata.keys()].join(\",\"));\r\n  }\r\n\r\n  async rebuildTree() {\r\n    await this.load();\r\n  }\r\n\r\n  async buildTree(mapName: string): Promise<DitamapNode | null> {\r\n    if (this.context.workspace.has(mapName)) {\r\n      return await this._treeBfs(this.context.workspace.get(mapName)!);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Builds json tree from xml\r\n   * @param rootHtml - parsed xml element\r\n   */\r\n  async _treeBfs(rootHtml: HTMLElement): Promise<DitamapNode> {\r\n    let tree = new RootMapNode({\r\n      rootElement: rootHtml,\r\n      rootMapName: this.rootMapName,\r\n      context: this.context,\r\n    });\r\n\r\n    let Q: QueueItem[] = [[rootHtml, tree, this.rootMapName]];\r\n    while (Array.isArray(Q) && Q.length > 0) {\r\n      let [element, parent, mapName] = Q.shift() as QueueItem;\r\n\r\n      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {\r\n        switch (DitamapUtils.getDitamapNodeClass(child)) {\r\n          case \"MapReferenceNode\": {\r\n            let structuralEl = await this._getStructuralElement(\r\n              child.getAttribute(\"href\")!\r\n            );\r\n\r\n            if (!structuralEl) {\r\n              console.warn(\"No structural element found for\", child);\r\n            }\r\n\r\n            structuralEl = structuralEl ?? child;\r\n\r\n            let newMapReferenceNode = new MapReferenceNode({\r\n              refElement: child,\r\n              structuralElement: structuralEl,\r\n              containingMap: mapName,\r\n              context: this.context,\r\n            });\r\n\r\n            let newMapName = DitamapUtils.getMapName(child, this.rootMapName);\r\n            let mapNameToUse = newMapName ?? mapName;\r\n\r\n            if (!newMapName) {\r\n              console.warn(\"No map name found for\", child);\r\n            }\r\n\r\n            Q.push([structuralEl, newMapReferenceNode, mapNameToUse]);\r\n            parent.children.push(newMapReferenceNode);\r\n            this._addToMetadataMap(mapNameToUse);\r\n            continue;\r\n          }\r\n\r\n          case \"StructuralNode\": {\r\n            let structuralNode = new StructuralNode(child, mapName);\r\n            Q.push([child, structuralNode, mapName]);\r\n            parent.children.push(structuralNode);\r\n            continue;\r\n          }\r\n\r\n          case \"TopicReferenceNode\": {\r\n            let contentNode = new TopicReferenceNode({\r\n              refElement: child,\r\n              containingMap: mapName,\r\n              context: this.context,\r\n            });\r\n            Q.push([child, contentNode, mapName]);\r\n            parent.children.push(contentNode);\r\n            if (child.getAttribute(\"href\")) {\r\n              this._addToMetadataMap(child.getAttribute(\"href\")!);\r\n            }\r\n            continue;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return tree;\r\n  }\r\n\r\n  _addToMetadataMap(href: string) {\r\n    if (this.context.metadata.has(href)) {\r\n      return;\r\n    }\r\n    this.context.metadata.set(href, null);\r\n  }\r\n\r\n  /** Adds ids to xml which will be stable accross tree operations */\r\n  _addIdsToXml(rootEl: HTMLElement): void {\r\n    let Q = [rootEl];\r\n\r\n    if (!rootEl.getAttribute(\"data-id\")) {\r\n      rootEl.setAttribute(\"data-id\", uuidv4());\r\n    }\r\n\r\n    while (Array.isArray(Q) && Q.length > 0) {\r\n      let el = Q.shift() as HTMLElement;\r\n\r\n      if (!el.getAttribute(\"data-id\")) {\r\n        el.setAttribute(\"data-id\", uuidv4());\r\n      }\r\n\r\n      for (let child of el.children as HTMLCollectionOf<HTMLElement>) {\r\n        Q.push(child);\r\n      }\r\n    }\r\n  }\r\n\r\n  async _getStructuralElement(href: string) {\r\n    if (this.context.workspace.has(href)) {\r\n      return this.context.workspace.get(href);\r\n    }\r\n\r\n    await this._fetchMap(href);\r\n\r\n    return this.context.workspace.get(href);\r\n  }\r\n\r\n  /** -- Fetch calls -- */\r\n\r\n  async _fetchMetadata(filenames: string) {\r\n    try {\r\n      const response = await fetch(\r\n        `/lwa/jrest/GetMetaDataForResList?cdlResLblIds=${filenames}`\r\n      );\r\n      if (!response.ok) {\r\n        throw Error();\r\n      }\r\n      const data: FileMeta[] = await response.json();\r\n      for (let file of data) {\r\n        this.context.metadata.set(file.resLblId, file);\r\n      }\r\n    } catch (e) {\r\n      console.log(e);\r\n    }\r\n  }\r\n\r\n  async _fetchMap(reslblId: string) {\r\n    if (this.context.workspace.has(reslblId)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`/lwa/jrest/GetTopicRenderXml${reslblId}`);\r\n      if (!response.ok) {\r\n        throw Error();\r\n      }\r\n\r\n      let textResponse = await response.text();\r\n      if (!textResponse) {\r\n        throw Error();\r\n      }\r\n\r\n      let parser = new DOMParser();\r\n      let xml = parser.parseFromString(textResponse, \"application/xml\");\r\n      this._addIdsToXml(xml.documentElement); // Add ids to xml\r\n      this.context.workspace.set(reslblId, xml.documentElement);\r\n\r\n      // Find submaps\r\n      let iterator = xml.evaluate(\r\n        '//*[@format=\"ditamap\"]',\r\n        xml.documentElement,\r\n        null,\r\n        XPathResult.ORDERED_NODE_ITERATOR_TYPE,\r\n        null\r\n      );\r\n\r\n      let current = iterator.iterateNext();\r\n      while (current) {\r\n        if (current.getAttribute(\"href\")) {\r\n          await this._fetchMap(current.getAttribute(\"href\"));\r\n        }\r\n        current = iterator.iterateNext();\r\n      }\r\n    } catch (e) {\r\n      console.log(e);\r\n    }\r\n  }\r\n\r\n  // -- Helpers --\r\n}\r\n\r\ntype QueueItem = [HTMLElement, DitamapNode, string];\r\n\r\nexport interface DitamapContext {\r\n  workspace: Map<string, HTMLElement>;\r\n  metadata: Map<string, FileMeta | null>;\r\n  dirty: Set<string>;\r\n  cutId: string | null;\r\n  buffer: DitamapNode | null;\r\n}\r\n\r\n/**\r\n *\r\n * The traversal should not include map or bookmap elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#\r\n */\r\n"]}