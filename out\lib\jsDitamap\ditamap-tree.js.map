{"version": 3, "file": "ditamap-tree.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EAEX,qBAAqB,EACrB,WAAW,GACZ,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,MAAM,OAAO,WAAW;IActB,YAAY,WAAmB,EAAE,KAAgC;QATjE;;WAEG;QACH,SAAI,GAAuB,IAAI,CAAC;QAO9B,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,SAAS,CAAC,OAAe;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,QAAqB;QAC5B,IAAI,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEvD,IAAI,CAAC,GAAgB,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAe,CAAC;YAExD,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAyC,EAAE,CAAC;gBACpE,IAAI,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrC,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACnC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAW,CACrC,CAAC;oBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;oBACzD,CAAC;oBAED,YAAY,GAAG,YAAY,IAAI,KAAK,CAAC;oBAErC,IAAI,iBAAiB,GAAG,IAAI,qBAAqB,CAC/C,KAAK,EACL,YAAY,EACZ,OAAO,CACR,CAAC;oBAEF,IAAI,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAClE,IAAI,YAAY,GAAG,UAAU,IAAI,OAAO,CAAC;oBAEzC,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBAC/C,CAAC;oBAED,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC;oBACxD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC1C,CAAC;qBAAM,IAAI,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9C,IAAI,WAAW,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBAClD,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAID,uCAAuC;AACvC,WAAW;AACX,eAAe;AACf,iBAAiB;AACjB,kBAAkB;AAClB,MAAM;AAEN,mEAAmE;AAEnE,wEAAwE;AAExE;;;GAGG", "sourcesContent": ["import {\r\n  ContentNode,\r\n  DitamapNode,\r\n  MapRefLikeElementNode,\r\n  RootMapNode,\r\n} from \"./ditamap-node\";\r\nimport { DitamapUtils } from \"./ditamap-utils\";\r\n\r\nexport class DitamapTree {\r\n  /**\r\n   * Map of all the stored ditamap xml\r\n   */\r\n  workspace: Map<string, HTMLElement>;\r\n  /**\r\n   * Underlying tree data structure\r\n   */\r\n  root: DitamapNode | null = null;\r\n  /**\r\n   * Name of the root map\r\n   */\r\n  rootMapName: string;\r\n\r\n  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {\r\n    this.workspace = files ?? new Map();\r\n    this.root = this.buildTree(rootMapName);\r\n    this.rootMapName = rootMapName;\r\n  }\r\n\r\n  buildTree(mapName: string): DitamapNode | null {\r\n    if (this.workspace.has(mapName)) {\r\n      return this._treeBfs(this.workspace.get(mapName)!);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Builds json tree from xml\r\n   * @param rootHtml - parsed xml element\r\n   */\r\n  _treeBfs(rootHtml: HTMLElement): DitamapNode {\r\n    let tree = new RootMapNode(rootHtml, this.rootMapName);\r\n\r\n    let Q: QueueItem[] = [[rootHtml, tree, this.rootMapName]];\r\n    while (Array.isArray(Q) && Q.length > 0) {\r\n      let [element, parent, mapName] = Q.shift() as QueueItem;\r\n\r\n      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {\r\n        if (DitamapUtils.isMapRefLike(child)) {\r\n          let structuralEl = this.workspace.get(\r\n            child.getAttribute(\"href\") as string\r\n          );\r\n\r\n          if (!structuralEl) {\r\n            console.warn(\"No structural element found for\", child);\r\n          }\r\n\r\n          structuralEl = structuralEl ?? child;\r\n\r\n          let newMapRefLikeNode = new MapRefLikeElementNode(\r\n            child,\r\n            structuralEl,\r\n            mapName\r\n          );\r\n\r\n          let newMapName = DitamapUtils.getMapName(child, this.rootMapName);\r\n          let mapNameToUse = newMapName ?? mapName;\r\n\r\n          if (!newMapName) {\r\n            console.warn(\"No map name found for\", child);\r\n          }\r\n\r\n          Q.push([structuralEl, newMapRefLikeNode, mapNameToUse]);\r\n          parent.children.push(newMapRefLikeNode);\r\n        } else if (DitamapUtils.isTopicRefLike(child)) {\r\n          let contentNode = new ContentNode(child, mapName);\r\n          Q.push([child, contentNode, mapName]);\r\n          parent.children.push(contentNode);\r\n        }\r\n      }\r\n    }\r\n\r\n    return tree;\r\n  }\r\n}\r\n\r\ntype QueueItem = [HTMLElement, DitamapNode, string];\r\n\r\n// const structuralElements = new Set([\r\n//   \"map\",\r\n//   \"bookmap\",\r\n//   \"topichead\",\r\n//   \"topicgroup\",\r\n// ]);\r\n\r\n// const referenceElements = new Set([\"mapref\", \"part\", \"keydef\"]);\r\n\r\n// const dualUseElements = new Set([\"topicref\", \"chapter\", \"appendix\"]);\r\n\r\n/**\r\n *\r\n * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#\r\n */\r\n"]}