{"version": 3, "file": "wizard-dialog.js", "sourceRoot": "", "sources": ["../../../src/components/common/wizard-dialog.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,kDAAkD;AAClD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,yCAAyC,CAAC;AACjD,OAAO,cAAc,CAAC;AACtB,OAAO,gBAAgB,CAAC;AACxB,kCAAkC;AAClC,kDAAkD;AAClD,OAAO,qCAAqC,CAAC;AAE7C,OAAO,qBAAqB,CAAC;AAC7B,OAAO,UAAU,CAAC;AAGX,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,UAAU;IAAxC;;QACwB,SAAI,GAAY,KAAK,CAAC;QACvB,eAAU,GAAW,EAAE,CAAC;QACxB,UAAK,GAAW,MAAM,CAAC;QACvB,WAAM,GAAW,MAAM,CAAC;QACxB,4BAAuB,GAAkB,IAAI,CAAC;QAC9C,WAAM,GAAwB,EAAE,CAAC;QACjC,iBAAY,GAAW,OAAO,CAAC;QAC/B,SAAI,GAAW,KAAK,CAAC;QACxC,UAAK,GAAW,EAAE,CAAC;QA6FpB,cAAS,GAAG,KAAK,IAAI,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;gBAC1C,WAAW;gBACX,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;gBAC9D,IAAI,CAAC,OAAO;oBAAE,OAAO,KAAK,CAAC,CAAC,kCAAkC;YAChE,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YAE/C,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjB,+DAA+D;gBAC/D,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,kBAAkB,EAAE;oBAClC,MAAM,EAAE,IAAI,CAAC,KAAK;oBAClB,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;iBACf,CAAC,CACH,CAAC;gBACF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;YAErB,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEM,aAAQ,GAAG,KAAK,IAAI,EAAE;YAC5B,uCAAuC;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;gBAC7C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB;gBAEjE,+CAA+C;gBAC/C,mEAAmE;gBAEnE,mDAAmD;YACrD,CAAC;YACD,2DAA2D;YAC3D,wBAAwB;QAC1B,CAAC,CAAC;IA4BJ,CAAC;IA9JC,MAAM,KAAK,MAAM;QACf,+BAA+B;QAC/B,qBAAqB;QACrB,wCAAwC;QACxC,sCAAsC;QACtC,2BAA2B;QAC3B,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,IAAI;QACJ,WAAW;QACX,gBAAgB;QAChB,IAAI;QACJ,qBAAqB;QACrB,iBAAiB;QACjB,mBAAmB;QACnB,yBAAyB;QACzB,IAAI;QACJ,oBAAoB;QACpB,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BT,CAAC;IACJ,CAAC;IAEM,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;IACjC,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;IAC/B,CAAC;IAES,YAAY,KAAI,CAAC;IAEjB,OAAO,CAAC,iBAAyD;QACzE,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAED,6CAA6C;IAErC,IAAI,KAAI,CAAC;IAET,aAAa;QACnB,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3C,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,IAAI,SAAS,CAAC;IAC3D,CAAC;IAEO,cAAc;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,IAAI,QAAQ,CAAC;IACzD,CAAC;IAED,8BAA8B;IAEtB,eAAe;QACrB,OAAO,IAAI,CAAA;gBACC,IAAI,CAAC,MAAM;eACZ,IAAI,CAAC,KAAK;8BACK,CAAC;IAC7B,CAAC;IA2CS,MAAM;QACd,OAAO,IAAI,CAAA;;gBAEC,IAAI,CAAC,IAAI;qBACJ,IAAI,CAAC,SAAS;oBACf,IAAI,CAAC,QAAQ;wBACT,IAAI,CAAC,eAAe,EAAE;uBACvB,IAAI,CAAC,cAAc,EAAE;sBACtB,IAAI,CAAC,aAAa,EAAE;iBACzB,IAAI,CAAC,KAAK;kBACT,IAAI,CAAC,MAAM;mCACM,IAAI,CAAC,uBAAuB;;;wCAGvB,IAAI,CAAC,eAAe,EAAE;;;;;;KAMzD,CAAC;QACF,mCAAmC;QACnC,aAAa;QACb,2BAA2B;QAC3B,uDAAuD;IACzD,CAAC;CACF,CAAA;AAxK8B;IAA5B,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;6CAAuB;AACvB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;mDAAyB;AACxB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CAAwB;AACvB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAyB;AACxB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gEAA+C;AAC9C;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAkC;AACjC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;qDAAgC;AAC/B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;6CAAsB;AACxC;IAAR,KAAK,EAAE;8CAAoB;AATjB,eAAe;IAD3B,aAAa,CAAC,mBAAmB,CAAC;GACtB,eAAe,CAyK3B", "sourcesContent": ["import { LitElement, html, css } from \"lit-element\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// import { storeInstance } from \"store/index.js\";\r\nimport \"@ui5/webcomponents/dist/Select.js\";\r\nimport \"@ui5/webcomponents/dist/Dialog.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/FileUploader.js\";\r\nimport \"../wex-table\";\r\nimport \"./list-builder\";\r\n// import pubsub from \"pubsub-js\";\r\n// import { httpError } from \"store/httpError.js\";\r\nimport \"@ui5/webcomponents/dist/RadioButton\";\r\n\r\nimport \"./wizard-stage-tabs\";\r\nimport \"./dialog\";\r\n\r\n@customElement(\"wex-wizard-dialog\")\r\nexport class WexWizardDialog extends LitElement {\r\n  @property({ type: Boolean }) open: boolean = false;\r\n  @property({ type: String }) headerText: string = \"\";\r\n  @property({ type: String }) width: string = \"auto\";\r\n  @property({ type: String }) height: string = \"auto\";\r\n  @property({ type: String }) dialogOpenStateProperty: string | null = null;\r\n  @property({ type: Object }) stages: Record<string, any> = {};\r\n  @property({ type: String }) initialStage: string = \"basic\";\r\n  @property({ type: String }) mode: string = \"Add\";\r\n  @state() stage: string = \"\";\r\n\r\n  static get styles() {\r\n    //   .table-caption-container {\r\n    //     display: flex;\r\n    //     margin-bottom: var(--spacing-sm);\r\n    //     justify-content: space-between;\r\n    //     align-items: center;\r\n    //   }\r\n    // ui5-label {\r\n    //   width: 15%;\r\n    // }\r\n    // .input {\r\n    //   width: 85%;\r\n    // }\r\n    // .input-container {\r\n    //   width: 100%;\r\n    //   display: flex;\r\n    //   align-items: center;\r\n    // }\r\n    // min-width: 60rem;\r\n    return css`\r\n      .dialog-container {\r\n        padding: var(--spacing-lg) var(--spacing-lg);\r\n        transition: height 0.3s ease-out;\r\n        overflow: hidden;\r\n      }\r\n\r\n      .body-container {\r\n        height: 100%;\r\n        justify-content: space-between;\r\n        display: flex;\r\n        align-items: flex-start;\r\n        transition:\r\n          transform 0.3s ease-out,\r\n          opacity 0.3s ease-out;\r\n      }\r\n\r\n      .tabs-container {\r\n        width: 20%;\r\n      }\r\n\r\n      .template-container {\r\n        width: 80%;\r\n        display: block;\r\n        flex: 1;\r\n        align-self: stretch;\r\n      }\r\n    `;\r\n  }\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    this.stage = this.initialStage;\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n  }\r\n\r\n  protected firstUpdated() {}\r\n\r\n  protected updated(changedProperties: Map<string | number | symbol, unknown>) {\r\n    if (changedProperties.has(\"open\")) {\r\n      this.init();\r\n    }\r\n  }\r\n\r\n  // stateChange(state: Record<string, any>) {}\r\n\r\n  private init() {}\r\n\r\n  private setHeaderText() {\r\n    return this.mode + \" \" + this.headerText;\r\n  }\r\n\r\n  private setConfirmLabel() {\r\n    return this.stages[this.stage].confirmLabel || \"Confirm\";\r\n  }\r\n\r\n  private setCancelLabel() {\r\n    return this.stages[this.stage].cancelLabel || \"Cancel\";\r\n  }\r\n\r\n  // private setDialogStage() {}\r\n\r\n  private renderStageTabs() {\r\n    return html`<wex-wizard-stage-tabs\r\n      .stages=${this.stages}\r\n      .stage=${this.stage}\r\n    ></wex-wizard-stage-tabs>`;\r\n  }\r\n\r\n  private onConfirm = async () => {\r\n    if (this.stages[this.stage].confirmAction) {\r\n      // TEMP EJS\r\n      const success = await this.stages[this.stage].confirmAction();\r\n      if (!success) return false; // bail out if confirmAction fails\r\n    }\r\n    const nextIdx = Object.keys(this.stages).indexOf(this.stage) + 1;\r\n    this.stage = Object.keys(this.stages)[nextIdx];\r\n\r\n    if (!!this.stage) {\r\n      // if there is a stage (next stage) then don't close the dialog\r\n      this.dispatchEvent(\r\n        new CustomEvent(\"set-wizard-stage\", {\r\n          detail: this.stage,\r\n          bubbles: true,\r\n          composed: true,\r\n        })\r\n      );\r\n      return false;\r\n    }\r\n\r\n    this.stage = \"basic\";\r\n\r\n    return true;\r\n  };\r\n\r\n  private onCancel = async () => {\r\n    // console.log(\"cancel wizard dialog\");\r\n    if (this.stages[this.stage].cancelAction) {\r\n      await this.stages[this.stage].cancelAction();\r\n      this.stage = Object.keys(this.stages)[0]; // set stage to initial\r\n\r\n      //   console.log(\"callback success?\", success);\r\n      //   if (!success) return false; // bail out if confirmAction fails\r\n\r\n      //   await this.stages[this.stage].confirmAction();\r\n    }\r\n    // not working, but what is correct behavior here? TODO EJS\r\n    // this.stage = \"basic\";\r\n  };\r\n\r\n  protected render() {\r\n    return html`\r\n      <wex-dialog\r\n        .open=${this.open}\r\n        .onConfirm=${this.onConfirm}\r\n        .onCancel=${this.onCancel}\r\n        .confirmLabel=${this.setConfirmLabel()}\r\n        .cancelLabel=${this.setCancelLabel()}\r\n        .headerText=${this.setHeaderText()}\r\n        .width=${this.width}\r\n        .height=${this.height}\r\n        .dialogOpenStateProperty=${this.dialogOpenStateProperty}\r\n      >\r\n        <div class=\"body-container\">\r\n          <div class=\"tabs-container\">${this.renderStageTabs()}</div>\r\n          <div class=\"template-container\">\r\n            <slot></slot>\r\n          </div>\r\n        </div>\r\n      </wex-dialog>\r\n    `;\r\n    //   <div class=\"dialog-container\">\r\n    //     </div>\r\n    // ${this.dialogTemplate()}\r\n    //   <div class=\"footer\">${this.footerTemplate()}</div>\r\n  }\r\n}\r\n"]}