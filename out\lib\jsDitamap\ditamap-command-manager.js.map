{"version": 3, "file": "ditamap-command-manager.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-command-manager.ts"], "names": [], "mappings": "AAIA,MAAM,OAAO,qBAAqB;IAGhC,YACU,IAAiB,EACjB,OAAoC;QADpC,SAAI,GAAJ,IAAI,CAAa;QACjB,YAAO,GAAP,OAAO,CAA6B;QAJ9C,YAAO,GAAc,EAAE,CAAC;IAKrB,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAgB;QAC5B,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,CAAC,CAAC;IACrC,CAAC;IAED,IAAI;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;IAC7B,CAAC;CACF;AAOD,MAAM,OAAO,aAAa;IACxB,YACU,IAAiB,EACjB,IAAc;QADd,SAAI,GAAJ,IAAI,CAAa;QACjB,SAAI,GAAJ,IAAI,CAAU;IACrB,CAAC;IAEJ,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CACF", "sourcesContent": ["import { DitamapTree } from \"./ditamap-tree\";\r\nimport { DitamapNode } from \"./ditamap-node\";\r\nimport { FileMeta } from \"@bds/types\";\r\n\r\nexport class DitamapCommandManager {\r\n  history: Command[] = [];\r\n\r\n  constructor(\r\n    private tree: DitamapTree,\r\n    private setRoot: (root: DitamapNode) => void\r\n  ) {}\r\n\r\n  async execute(command: Command) {\r\n    command.execute();\r\n    this.history.push(command);\r\n    await this.tree.rebuildTree();\r\n    this.setRoot(this.tree.getRoot()!);\r\n  }\r\n\r\n  undo() {\r\n    this.history.pop()?.undo();\r\n  }\r\n}\r\n\r\nexport interface Command {\r\n  execute(): void;\r\n  undo(): void;\r\n}\r\n\r\nexport class InsertCommand implements Command {\r\n  constructor(\r\n    private node: DitamapNode,\r\n    private file: FileMeta\r\n  ) {}\r\n\r\n  execute() {\r\n    this.node.insertNodeFromFile(this.file);\r\n  }\r\n\r\n  undo() {\r\n    console.log(\"Undo\");\r\n  }\r\n}\r\n"]}