export declare const createXmlElement: (xml: string) => HTMLElement;
export declare const PART_SUBMAP_PHONES = "\n      <part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\n        class=\"- map/topicref bookmap/part \" />\n    ";
export declare const MAP_SUBMAP_PHONES = "\n        <map title=\"Submap Phones\"\n    class=\"- map/map \"></map>\n      ";
export declare const MAP_DITA20 = "<map xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\" xml:lang=\"en-US\" class=\"- map/map \">\n    <title class=\"- topic/title \">DITA 2.0 Phone Quickstart Guide</title>\n    <topicref href=\"/Content/getting_started_xi1699_1_1.dita\" class=\"- map/topicref \"/>\n    <topicref href=\"/Content/charge_the_battery_xi1698_1_1.dita\" class=\"- map/topicref \"/>\n    <topicref href=\"/Content/quickstart_mp4_video_xi1700_1_1.dita\" class=\"- map/topicref \"/>\n</map>";
