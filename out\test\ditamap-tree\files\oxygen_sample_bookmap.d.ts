export declare const OXYGEN_SAMPLE_BOOKMAP = "<bookmap id=\"taskbook\">\n <booktitle>\n   <mainbooktitle>Product tasks</mainbooktitle>\n   <booktitlealt>Tasks and what they do</booktitlealt>\n </booktitle>\n <bookmeta>\n   <author><PERSON></author>\n   <bookrights>\n     <copyrfirst>\n       <year>2006</year>\n     </copyrfirst>\n     <bookowner>\n       <person href=\"janedoe.dita\"><PERSON></person>\n     </bookowner>\n   </bookrights>\n </bookmeta>\n <frontmatter>\n   <preface/>\n </frontmatter>\n   <chapter format=\"ditamap\" href=\"installing.ditamap\"/>\n   <chapter href=\"configuring.dita\"/>\n   <chapter href=\"maintaining.dita\">\n     <topicref href=\"maintainstorage.dita\"/>\n     <topicref href=\"maintainserver.dita\"/>\n     <topicref href=\"maintaindatabase.dita\"/>\n   </chapter>\n <appendix href=\"task_appendix.dita\"/>\n</bookmap>";
export declare const INSTALLING_DITAMAP = "<map title=\"Installing\">\n  <topicref href=\"install_overview.dita\"/>\n  <topicref href=\"system_requirements.dita\"/>\n  <topicref href=\"download_software.dita\"/>\n  <topicref href=\"install_steps.dita\">\n    <topicref href=\"install_step1.dita\"/>\n    <topicref href=\"install_step2.dita\"/>\n    <topicref href=\"install_step3.dita\"/>\n  </topicref>\n  <topicref href=\"verify_installation.dita\"/>\n</map>";
//# sourceMappingURL=oxygen_sample_bookmap.d.ts.map