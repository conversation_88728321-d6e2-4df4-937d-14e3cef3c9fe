export declare const OXYGEN_SAMPLE_BOOKMAP = "<bookmap id=\"taskbook\">\n <booktitle>\n   <mainbooktitle>Product tasks</mainbooktitle>\n   <booktitlealt>Tasks and what they do</booktitlealt>\n </booktitle>\n <bookmeta>\n   <author><PERSON></author>\n   <bookrights>\n     <copyrfirst>\n       <year>2006</year>\n     </copyrfirst>\n     <bookowner>\n       <person href=\"janedoe.dita\"><PERSON></person>\n     </bookowner>\n   </bookrights>\n </bookmeta>\n <frontmatter>\n   <preface/>\n </frontmatter>\n   <chapter format=\"ditamap\" href=\"installing.ditamap\"/>\n   <chapter href=\"configuring.dita\"/>\n   <chapter href=\"maintaining.dita\">\n     <topicref href=\"maintainstorage.dita\"/>\n     <topicref href=\"maintainserver.dita\"/>\n     <topicref href=\"maintaindatabase.dita\"/>\n   </chapter>\n <appendix href=\"task_appendix.dita\"/>\n</bookmap>";
//# sourceMappingURL=oxygen_sample_bookmap.d.ts.map