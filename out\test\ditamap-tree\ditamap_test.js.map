{"version": 3, "file": "ditamap_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap_test.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AACH,OAAO,EAEL,qBAAqB,EACrB,WAAW,GACZ,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC5B,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C,gBAAgB,EAChB,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC1C,aAAa,EACb,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAElB,IAAI,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC/C,SAAS,CAAC,GAAG,CAAC,4CAA4C,EAAE,OAAO,CAAC,CAAC;QACrE,SAAS,CAAC,GAAG,CAAC,2CAA2C,EAAE,MAAM,CAAC,CAAC;QAEnE,IAAI,IAAI,GAAG,IAAI,WAAW,CACxB,4CAA4C,EAC5C,SAAS,CACV,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAChE,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;YACnB,MAAM;YACN,UAAU;YACV,mBAAmB;SACpB,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;0CACoC,EACpC,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;qDAC+C,EAC/C,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACzE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,2EAA2E,EAC3E,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACzB,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,8FAA8F,EAC9F,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,gGAAgG,EAChG,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;;4CAEsC,EACtC,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE;QACvB,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;0CACoC,EACpC,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;IAC5E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC1D,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;;4CAEsC,EACtC,iBAAiB,CAClB,CAAC;QACF,MAAM,CACJ,YAAY,CAAC,UAAU,CACrB,QAAQ,CAAC,eAAe,EACxB,4CAA4C,CAC7C,CACF,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C;;;;;;;;WAQK,EACL,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,IAAI,GAAG,IAAI,WAAW,CACxB,OAAO,EACP,4CAA4C,CAC7C,CAAC;QACN,CAAC;QACG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,EAAE,4CAA4C;YAClD,aAAa,EAAE,IAAI;YACnB,KAAK,EAAE,oBAAoB;YAC3B,UAAU,EAAE,IAAI;YAChB,iBAAiB,EAAE,OAAO;YAC1B,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC3C;;;KAGD,EACC,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,iBAAiB,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CACrD;;;OAGC,EACD,iBAAiB,CAClB,CAAC,eAAe,CAAC;QAClB,IAAI,IAAI,GAAG,IAAI,qBAAqB,CAClC,OAAO,EACP,iBAAiB,EACjB,4CAA4C,CAC7C,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,EAAE,2CAA2C;YACjD,aAAa,EAAE,4CAA4C;YAC3D,KAAK,EAAE,eAAe;YACtB,UAAU,EAAE,OAAO;YACnB,iBAAiB,EAAE,iBAAiB;YACpC,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,SAAS,GAAG,CAChB,OAA2B,EAC3B,MAAmB,EACnB,EAAE;IACF,IAAI,CAAC,OAAO;QAAE,OAAO,IAAI,CAAC;IAE1B,IAAI,MAAM,GAAM,EAAO,CAAC;IAExB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS;YAAE,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5E,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["/**\r\n * The ditamap editor involves many parts\r\n *  1. Storing a hashmap of the files\r\n *  2. Building a JSON tree from the xml\r\n *  3. Displaying the tree\r\n *  4. Editing the tree\r\n *  5. Saving the tree\r\n *  6. Validating the tree\r\n */\r\nimport {\r\n  DitamapNode,\r\n  MapRefLikeElementNode,\r\n  RootMapNode,\r\n} from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { DitamapTree } from \"../../lib/jsDitamap/ditamap-tree\";\r\nimport { DitamapUtils } from \"../../lib/jsDitamap/ditamap-utils\";\r\nimport { PHONES_TREE } from \"./data/json-trees\";\r\nimport { PHONES_1_BOOKMAP, SUBMAP_PHONES } from \"./files/phones_1_bookmap\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Building JSON tree from xml\", () => {\r\n  it(\"builds phones tree\", () => {\r\n    let bookmap = new DOMParser().parseFromString(\r\n      PHONES_1_BOOKMAP,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let submap = new DOMParser().parseFromString(\r\n      SUBMAP_PHONES,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    \r\n    let workspace = new Map<string, HTMLElement>();\r\n    workspace.set(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\", bookmap);\r\n    workspace.set(\"/Content/submap_phones_xi1609_1_1.ditamap\", submap);\r\n\r\n    let tree = new DitamapTree(\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      workspace\r\n    );\r\n\r\n    expect(pruneTree(PHONES_TREE, [\"href\", \"children\"])).to.deep.equal(\r\n      pruneTree(tree.root, [\r\n        \"href\",\r\n        \"children\",\r\n        // \"containingMap\",\r\n      ])\r\n    );\r\n  });\r\n});\r\n\r\ndescribe(\"Is Map reference\", () => {\r\n  it(\"part with href and format=ditamap => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n\t\tclass=\"- map/topicref bookmap/part \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"mapref with href and format=ditamap => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<mapref format=\"ditamap\" href=\"/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap\"\r\n        class=\"+ map/topicref mapgroup-d/mapref \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isMapRefLike(document.documentElement)).to.be.true;\r\n  });\r\n});\r\n\r\ndescribe(\"Is TopicRefLike\", () => {\r\n  it(\"topicref => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<topicref href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"chapter => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<chapter href=\"/Content/Introduction_xi1674_1_1.xml\" class=\"- map/topicref bookmap/chapter\">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"topichead => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<topichead href=\"/Content/Introduction_xi1674_1_1.xml\" class=\"- map/topicref bookmap/chapter\">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"bookmap => false\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n        id=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n        class=\"- map/map bookmap/bookmap \">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;\r\n  });\r\n\r\n  it(\"part => false\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n\t\tclass=\"- map/topicref bookmap/part \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapUtils.isTopicRefLike(document.documentElement)).to.be.false;\r\n  });\r\n});\r\n\r\ndescribe(\"Get map name (href)\", () => {\r\n  it(\"If the element is a bookmap, use the rootMapName\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n        id=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n        class=\"- map/map bookmap/bookmap \">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(\r\n      DitamapUtils.getMapName(\r\n        document.documentElement,\r\n        \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n      )\r\n    ).to.equal(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\");\r\n  });\r\n});\r\ndescribe(\"Root Map Node\", () => {\r\n  it(\"should create node from bookmap\", () => {\r\n    let element = new DOMParser().parseFromString(\r\n      `\r\n        <bookmap \r\n        class=\"- map/map bookmap/bookmap \">\r\n        \t<booktitle class=\"- topic/title bookmap/booktitle \">\r\n\t\t<mainbooktitle class=\"- topic/ph bookmap/mainbooktitle \">\r\n\t\t\t<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>\r\n\t</booktitle>\r\n        </bookmap>\r\n          `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let node = new RootMapNode(\r\n      element,\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n    );\r\n;\r\n    expect(node).to.deep.equal({\r\n      href: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      containingMap: null,\r\n      title: \"Phone 1 User Guide\",\r\n      refElement: null,\r\n      structuralElement: element,\r\n      mapPath: [],\r\n      children: [],\r\n    });\r\n  });\r\n});\r\ndescribe(\"Map Ref Like Node\", () => {\r\n  it(\"should create node from part\", () => {\r\n    let element = new DOMParser().parseFromString(\r\n      `\r\n      <part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n\t\tclass=\"- map/topicref bookmap/part \" />\r\n    `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let structuralElement = new DOMParser().parseFromString(\r\n      `\r\n        <map title=\"Submap Phones\"\r\n    class=\"- map/map \"></map>\r\n      `,\r\n      \"application/xml\"\r\n    ).documentElement;\r\n    let node = new MapRefLikeElementNode(\r\n      element,\r\n      structuralElement,\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n    );\r\n    expect(node).to.deep.equal({\r\n      href: \"/Content/submap_phones_xi1609_1_1.ditamap\",\r\n      containingMap: \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      title: \"Submap Phones\",\r\n      refElement: element,\r\n      structuralElement: structuralElement,\r\n      mapPath: [],\r\n      children: [],\r\n    });\r\n  });\r\n});\r\n\r\nconst pruneTree = <T extends DitamapNode>(\r\n  current: DitamapNode | null,\r\n  fields: (keyof T)[]\r\n) => {\r\n  if (!current) return null;\r\n\r\n  let pruned: T = {} as T;\r\n\r\n  fields.forEach((field) => {\r\n    if (current[field] != undefined) pruned[field] = current[field];\r\n  });\r\n\r\n  pruned.children = current.children.map((child) => pruneTree(child, fields));\r\n  return pruned;\r\n};\r\n"]}