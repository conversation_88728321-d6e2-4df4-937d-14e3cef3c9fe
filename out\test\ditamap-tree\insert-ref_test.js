import { MapReferenceNode, RootMapNode, } from "../../lib/jsDitamap/ditamap-node";
import { expect } from "@open-wc/testing";
import { createXmlElement, MAP_SUBMAP_PHONES, PART_SUBMAP_PHONES, } from "./data/elements";
import * as metadata from "./files/metadata";
import { PHONES_1_BOOKMAP } from "./files/phones_1_bookmap";
describe("Map Reference Parent", () => {
    it("Inserts a concept as a topicref", () => {
        let refElement = createXmlElement(PART_SUBMAP_PHONES);
        let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
        let node = new MapReferenceNode({
            refElement: refElement,
            structuralElement: structuralElement,
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
        });
        node.insertNodeFromFile(metadata.BEGIN_FILE);
        expect(node.children.length).to.equal(1);
        expect(node.children[0].refElement.tagName).to.equal("topicref");
        expect(node.children[0].refElement.getAttribute("href")).to.equal("/Content/begin_xi1612_1_1.xml");
    });
    it("Inserts a glossentry as a glossref", () => {
        let refElement = createXmlElement(PART_SUBMAP_PHONES);
        let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
        let node = new MapReferenceNode({
            refElement: refElement,
            structuralElement: structuralElement,
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
        });
        node.insertNodeFromFile(metadata.DUAL_BAND_FILE);
        expect(node.children.length).to.equal(1);
        expect(node.children[0].refElement.tagName).to.equal("glossref");
        expect(node.children[0].refElement.getAttribute("href")).to.equal("/Content/dual_band_xi1582_1_1.xml");
    });
    it("Inserts a map as a mapref", () => {
        let refElement = createXmlElement(PART_SUBMAP_PHONES);
        let structuralElement = createXmlElement(MAP_SUBMAP_PHONES);
        let node = new MapReferenceNode({
            refElement: refElement,
            structuralElement: structuralElement,
            containingMap: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
        });
        node.insertNodeFromFile(metadata.DITA20_FILE);
        expect(node.children.length).to.equal(1);
        expect(node.children[0].refElement.tagName).to.equal("mapref");
        expect(node.children[0].refElement.getAttribute("href")).to.equal("/Content/dita20_quickstart_guide_xi1683_1_1.ditamap");
    });
    it.skip("Blocks a self reference", () => { });
    it.skip("Blocks a bookmap reference", () => { });
});
describe.only("Bookmap Parent", () => {
    it("Inserts a ditamap as a part", () => {
        let structuralElement = createXmlElement(PHONES_1_BOOKMAP);
        let parent = new RootMapNode({
            rootElement: structuralElement,
            rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
        });
        parent.insertNodeFromFile(metadata.DITA20_FILE);
        let target = parent.structuralElement.querySelector("[href='/Content/dita20_quickstart_guide_xi1683_1_1.ditamap']");
        expect(target.tagName).to.equal("part");
        expect(target.getAttribute("href")).to.equal("/Content/dita20_quickstart_guide_xi1683_1_1.ditamap");
    });
    it("Inserts a topicref as a chapter", () => {
        let structuralElement = createXmlElement(PHONES_1_BOOKMAP);
        let parent = new RootMapNode({
            rootElement: structuralElement,
            rootMapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
        });
        parent.insertNodeFromFile(metadata.BEGIN_FILE);
        let target = parent.structuralElement.querySelector("[href='/Content/begin_xi1612_1_1.xml']");
        expect(target.tagName).to.equal("chapter");
        expect(target.getAttribute("href")).to.equal("/Content/begin_xi1612_1_1.xml");
    });
    it.skip("Blocks a bookmap reference", () => { });
});
describe("Topic Reference Parent", () => {
    it.skip("Inserts a ditamap as a mapref", () => { });
    it.skip("Inserts a topicref as a topicref", () => { });
    it.skip("Blocks a bookmap reference", () => { });
});
//# sourceMappingURL=insert-ref_test.js.map