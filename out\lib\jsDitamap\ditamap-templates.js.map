{"version": 3, "file": "ditamap-templates.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-templates.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC9B,YAAsC,EACtC,IAAc,EACD,EAAE;IACf,IAAI,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAoB,CAAC;IAE5D,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,KAAK,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IACjE,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;IAC7C,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnD,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;IACjD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG;IACtB,KAAK,EAAE,mCAAmC;IAC1C,MAAM,EAAE,SAAS;IACjB,cAAc,EAAE,QAAQ;CACzB,CAAC;AAEF,MAAM,aAAa,GAAG;IACpB,KAAK,EAAE,8BAA8B;IACrC,MAAM,EAAE,SAAS;IACjB,cAAc,EAAE,MAAM;CACvB,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,KAAK,EAAE,iBAAiB;IACxB,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,UAAU;CAC3B,CAAC;AAEF,MAAM,gBAAgB,GAAG;IACvB,KAAK,EAAE,iCAAiC;IACxC,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,SAAS;CAC1B,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,KAAK,EAAE,qCAAqC;IAC5C,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,UAAU;CAC3B,CAAC;AAEF,MAAM,WAAW,GAAG;IAClB,MAAM,EAAE,eAAe;IACvB,IAAI,EAAE,aAAa;IACnB,OAAO,EAAE,gBAAgB;IACzB,QAAQ,EAAE,iBAAiB;IAC3B,QAAQ,EAAE,iBAAiB;CAC5B,CAAC", "sourcesContent": ["import { FileMeta } from \"@bds/types\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\n\r\nexport const createRefElement = (\r\n  templateName: keyof typeof templateMap,\r\n  file: FileMeta\r\n): HTMLElement => {\r\n  let template = templateMap[templateName] as ElementTemplate;\r\n\r\n  if (!template) {\r\n    throw Error(`No template found for ${templateName}`);\r\n  }\r\n\r\n  let newElement = document.createElement(template.elementTagName);\r\n  newElement.setAttribute(\"data-id\", uuidv4());\r\n  newElement.setAttribute(\"href\", file.resLblId);\r\n  newElement.setAttribute(\"format\", template.format);\r\n  newElement.setAttribute(\"class\", template.class);\r\n  return newElement;\r\n};\r\n\r\nconst MAPREF_TEMPLATE = {\r\n  class: \"- map/topicref mapgroup-d/mapref \",\r\n  format: \"ditamap\",\r\n  elementTagName: \"mapref\",\r\n};\r\n\r\nconst PART_TEMPLATE = {\r\n  class: \"- map/topicref bookmap/part \",\r\n  format: \"ditamap\",\r\n  elementTagName: \"part\",\r\n};\r\n\r\nconst TOPICREF_TEMPLATE = {\r\n  class: \"- map/topicref \",\r\n  format: \"dita\",\r\n  elementTagName: \"topicref\",\r\n};\r\n\r\nconst CHAPTER_TEMPLATE = {\r\n  class: \"- map/topicref bookmap/chapter \",\r\n  format: \"dita\",\r\n  elementTagName: \"chapter\",\r\n};\r\n\r\nconst GLOSSREF_TEMPLATE = {\r\n  class: \"+ map/topicref glossref-d/glossref \",\r\n  format: \"dita\",\r\n  elementTagName: \"glossref\",\r\n};\r\n\r\nconst templateMap = {\r\n  mapref: MAPREF_TEMPLATE,\r\n  part: PART_TEMPLATE,\r\n  chapter: CHAPTER_TEMPLATE,\r\n  topicref: TOPICREF_TEMPLATE,\r\n  glossref: GLOSSREF_TEMPLATE,\r\n};\r\n\r\ntype ElementTemplate = {\r\n  class: string;\r\n  format: string;\r\n  elementTagName: string;\r\n};\r\n"]}