export const OXYGE<PERSON>_SAMPLE_TREE: DitamapNodeStructure = {
  title: "Product tasks",
  type: "map",
  href: "/taskbook.ditamap",
  rootElementName: "bookmap",
  mapPath: [],
  children: [
    {
      type: "map",
      title: "Installing",
      href: "installing.ditamap",
      containingMap: "/taskbook.ditamap",
      mapPath: ["/taskbook.ditamap"],
      rootElementName: "map",
      children: [
        {
          type: "topicref",
          title: "Install Overview",
          href: "install_overview.dita",
          containingMap: "installing.ditamap",
          mapPath: ["/taskbook.ditamap", "installing.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topicref",
          title: "System Requirements",
          href: "system_requirements.dita",
          containingMap: "installing.ditamap",
          mapPath: ["/taskbook.ditamap", "installing.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topicref",
          title: "Download Software",
          href: "download_software.dita",
          containingMap: "installing.ditamap",
          mapPath: ["/taskbook.ditamap", "installing.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topicref",
          title: "Install Steps",
          href: "install_steps.dita",
          containingMap: "installing.ditamap",
          mapPath: ["/taskbook.ditamap", "installing.ditamap"],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Install Step 1",
              href: "install_step1.dita",
              containingMap: "installing.ditamap",
              mapPath: ["/taskbook.ditamap", "installing.ditamap"],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Install Step 2",
              href: "install_step2.dita",
              containingMap: "installing.ditamap",
              mapPath: ["/taskbook.ditamap", "installing.ditamap"],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Install Step 3",
              href: "install_step3.dita",
              containingMap: "installing.ditamap",
              mapPath: ["/taskbook.ditamap", "installing.ditamap"],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "Verify Installation",
          href: "verify_installation.dita",
          containingMap: "installing.ditamap",
          mapPath: ["/taskbook.ditamap", "installing.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
      ],
    },
    {
      type: "chapter",
      title: "Configuring",
      href: "configuring.dita",
      containingMap: "/taskbook.ditamap",
      mapPath: ["/taskbook.ditamap"],
      rootElementName: "chapter",
      children: [],
    },
    {
      type: "chapter",
      title: "Maintaining",
      href: "maintaining.dita",
      containingMap: "/taskbook.ditamap",
      mapPath: ["/taskbook.ditamap"],
      rootElementName: "chapter",
      children: [
        {
          type: "topicref",
          title: "Maintain Storage",
          href: "maintainstorage.dita",
          containingMap: "/taskbook.ditamap",
          mapPath: ["/taskbook.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topicref",
          title: "Maintain Server",
          href: "maintainserver.dita",
          containingMap: "/taskbook.ditamap",
          mapPath: ["/taskbook.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topicref",
          title: "Maintain Database",
          href: "maintaindatabase.dita",
          containingMap: "/taskbook.ditamap",
          mapPath: ["/taskbook.ditamap"],
          rootElementName: "topicref",
          children: [],
        },
      ],
    },
    {
      type: "appendix",
      title: "Task Appendix",
      href: "task_appendix.dita",
      containingMap: "/taskbook.ditamap",
      mapPath: ["/taskbook.ditamap"],
      rootElementName: "appendix",
      children: [],
    },
  ],
};