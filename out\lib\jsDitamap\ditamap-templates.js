import { v4 as uuidv4 } from "uuid";
export const createRefElement = (templateName, file) => {
    let template = templateMap[templateName];
    if (!template) {
        throw Error(`No template found for ${templateName}`);
    }
    let newElement = document.createElement(template.elementTagName);
    newElement.setAttribute("data-id", uuidv4());
    newElement.setAttribute("href", file.resLblId);
    newElement.setAttribute("format", template.format);
    newElement.setAttribute("class", template.class);
    return newElement;
};
const MAPREF_TEMPLATE = {
    class: "- map/topicref mapgroup-d/mapref ",
    format: "ditamap",
    elementTagName: "mapref",
};
const PART_TEMPLATE = {
    class: "- map/topicref bookmap/part ",
    format: "ditamap",
    elementTagName: "part",
};
const TOPICREF_TEMPLATE = {
    class: "- map/topicref ",
    format: "dita",
    elementTagName: "topicref",
};
const CHAPTER_TEMPLATE = {
    class: "- map/topicref bookmap/chapter ",
    format: "dita",
    elementTagName: "chapter",
};
const GLOSSREF_TEMPLATE = {
    class: "+ map/topicref glossref-d/glossref ",
    format: "dita",
    elementTagName: "glossref",
};
const templateMap = {
    mapref: MAPREF_TEMPLATE,
    part: PART_TEMPLATE,
    chapter: CHAPTER_TEMPLATE,
    topicref: TOPICREF_TEMPLATE,
    glossref: GLOSSREF_TEMPLATE,
};
//# sourceMappingURL=ditamap-templates.js.map