var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
import * as util from "lib/util";
// @ts-ignore
import * as wexlib from "lib/wexlib.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import "../base/col-item";
import "../common/wizard-dialog";
import "../common/list-picker";
import "../common/table-picker";
let WexDialogPublishDefsCreate = class WexDialogPublishDefsCreate extends LitElement {
    constructor() {
        super(...arguments);
        this.dialogOpenData = null;
        this.state = {};
        this.string = {};
        this.stage = "";
        this.pubContent = [];
        this.pubLangs = [];
        this.pubOutputs = [];
        this.selectedPubContent = [];
        this.selectedPubLangs = [];
        this.selectedPubOutputs = [];
        this.pubDef = null;
        this.pubDefId = -1;
        this.selectedProject = null;
        this.selectedProjectId = -1;
        this.selectedProjectName = "";
        this.selectedProjectDesc = "";
        this._name = "";
        this._desc = "";
        this.subscription = null;
        this.pubDefNames = [];
        this.initialStage = "basic";
        this.stages = {
            basic: {
                name: "basic",
                title: "Definition Name",
                confirmLabel: "Next",
                confirmAction: () => this.validate(),
                cancelAction: () => this.init(),
            },
            content: {
                name: "content",
                title: "Publication Content",
                confirmLabel: "Next",
                // confirmAction: () => this.confirmPubContent(this.pubContent),
                // confirmAction: () => this.setPubLangs(),
                confirmAction: () => this.validate(),
                cancelAction: () => this.init(),
            },
            languages: {
                name: "languages",
                title: "Languages",
                confirmLabel: "Next",
                confirmAction: () => this.validate(),
                cancelAction: () => this.init(),
            },
            outputs: {
                name: "outputs",
                title: "Output Formats",
                confirmLabel: "Next",
                confirmAction: () => this.validate(),
                cancelAction: () => this.init(),
            },
            filename: {
                name: "filename",
                title: "Output File Name",
                confirmLabel: "Save",
                confirmAction: () => this.confirm(),
                cancelAction: () => this.init(),
            },
        };
        this.mode = null;
        this._errors = {
            name: null,
            desc: null,
            project: null,
            selected: null,
        };
        this.validate = async () => {
            switch (this.stage) {
                case "basic":
                    if (!this._name || !this._name.length) {
                        this._errors.name = this.string["_error_empty_value"];
                    }
                    if (this.dialogOpenData?.mode === "Create" &&
                        this.pubDefNames.includes(this._name)) {
                        this._errors.name =
                            "A publish definition with this name already exists.";
                    }
                    if (!this._desc || !this._desc.length) {
                        this._errors.desc = this.string["_error_empty_value"];
                    }
                    if (!this.selectedProjectId || this.selectedProjectId == -1) {
                        this._errors.project = "A project must be selected.";
                    }
                    if (!!this._errors.name ||
                        !!this._errors.desc ||
                        !!this._errors.project) {
                        this.requestUpdate();
                        return false;
                    }
                    this.setPubContent();
                    return true;
                case "content":
                    if (!this.selectedPubContent.length) {
                        this._errors.selected = "The selected column cannot be empty.";
                    }
                    else {
                        this._errors = {
                            name: null,
                            desc: null,
                            project: null,
                            selected: null,
                        };
                    }
                    if (!!this._errors.selected) {
                        this.requestUpdate();
                        return false;
                    }
                    return true;
                case "languages":
                    if (!this.selectedPubLangs.length) {
                        this._errors.selected = "The selected column cannot be empty.";
                    }
                    else {
                        this._errors = {
                            name: null,
                            desc: null,
                            project: null,
                            selected: null,
                        };
                    }
                    if (!!this._errors.selected) {
                        this.requestUpdate();
                        return false;
                    }
                    return true;
                case "outputs":
                    if (!this.selectedPubOutputs.length) {
                        this._errors.selected = "The Selected column cannot be empty.";
                    }
                    else {
                        this._errors = {
                            name: null,
                            desc: null,
                            project: null,
                            selected: null,
                        };
                    }
                    if (!!this._errors.selected) {
                        this.requestUpdate();
                        // console.log("validate is false");
                        return false;
                    }
                    // console.log("validate is true");
                    return true;
                default:
                    this._errors = {
                        name: null,
                        desc: null,
                        project: null,
                        selected: null,
                    };
                    return true;
            }
        };
        this.confirm = async () => {
            const isValid = await this.validate();
            if (!isValid) {
                // console.log("should close?", isValid);
                return false;
            }
            const body = {
                name: this._name,
                desc: this._desc,
                projectId: this.selectedProjectId,
                pubContent: this.selectedPubContent,
                pubLangs: this.selectedPubLangs,
                pubOutputs: this.selectedPubOutputs,
                pubDefId: -1,
            };
            // console.log("confirm: body", body);
            if (this.mode === "Edit") {
                body.pubDefId = this.dialogOpenData?.pubDef.pubDefId;
                await wexlib.editPubDef(body);
                util.notifyUser("positive", this.string["_msg_publish_defs_create_pubdef_success"]);
            }
            else {
                await wexlib.createPubDef(body);
                util.notifyUser("positive", this.string["_msg_publish_defs_create_pubdef_success"]);
            }
            // this.dispatchEvent(
            //   new CustomEvent("update-def-list", {
            //     bubbles: true,
            //     composed: true,
            //   })
            // );
            this.init(); // reset the dialog
            // refresh the API data
            const pubDefs = await wexlib.getAllPubDefs();
            if (pubDefs.length) {
                storeInstance.dispatch("setPubDefs", {
                    pubDefs,
                });
            }
            return true; // true closes the dialog
        };
        this.handlePickListUpdated = (e, stage) => {
            // console.log("handle pick list", e.detail);
            switch (stage) {
                case "content":
                    this.selectedPubContent = e.detail.selected;
                    this.pubContent = e.detail.unselected;
                    break;
                case "languages":
                    this.selectedPubLangs = e.detail.selected;
                    this.pubLangs = e.detail.unselected;
                    break;
                case "outputs":
                    this.selectedPubOutputs = e.detail.selected;
                    this.pubOutputs = e.detail.unselected;
                    break;
                default:
                    console.error("error in handlePickListUpdated");
            }
        };
    }
    static get styles() {
        return css `
      wex-row-item {
        color: var(--font-color);
        margin-bottom: 1rem;
      }

      wex-col-item {
        margin-bottom: 1rem;
      }

      ui5-input {
        width: 100%;
      }

      ui5-select {
        width: 100%;
      }

      .error-msg {
        font-style: italic;
        color: var(--clr-negative);
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this.init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    updated(changedProperties) {
        // console.log("type this:", this.pubContent);
        const dialog = this.shadowRoot?.querySelector("ui5-dialog");
        if (dialog &&
            changedProperties.has("dialogOpenData") &&
            this.dialogOpenData) {
            // console.log("dialogOpenData changed");
            this.init();
        }
        if (changedProperties.has("selectedProjectId") &&
            this.selectedProjectId >= 0) {
            this.setMode();
            this.setPubContent();
            this.setPubLangs();
            this.setPubOutputs();
        }
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        this.dialogOpenData = state.publish_defs_create_dialog_open;
        if (!!this.dialogOpenData) {
            this.setBasicInfo();
            // this.setPubContent();
            // this.setPubLangs();
            // this.setPubOutputs();
        }
    }
    setMode() {
        if (this.dialogOpenData && this.dialogOpenData.mode) {
            this.mode = this.dialogOpenData.mode;
        }
    }
    async setBasicInfo() {
        const pubDef = this.dialogOpenData?.pubDef;
        this._errors = {
            name: null,
            desc: null,
            project: null,
            selected: null,
        };
        const pubDefs = await wexlib.getAllPubDefs();
        if (pubDefs.length) {
            storeInstance.dispatch("setPubDefs", {
                pubDefs,
            });
        }
        this.pubDefNames = pubDefs.map((pubDef) => pubDef.name);
        if (pubDef) {
            this._name = pubDef.name;
            this._desc = pubDef.desc;
            this.selectedProjectId = pubDef.projectId;
        }
        else {
            this._name = "";
            this._desc = "";
            this.selectedProjectId = -1;
        }
    }
    async setPubContent() {
        const pubDef = this.dialogOpenData?.pubDef;
        const pubContent = await wexlib.getProjPubContents(this.selectedProjectId);
        this.pubContent = pubContent;
        if (pubDef) {
            this.selectedPubContent = pubDef.lstPubContentDtos;
            this.pubContent = pubContent.filter((content) => !pubDef.lstPubContentDtos.some((dto) => dto.pubContentId === content.pubContentId));
        }
    }
    async setPubLangs() {
        const pubDef = this.dialogOpenData?.pubDef;
        const pubLangs = await wexlib.getProjLanguages(this.selectedProjectId);
        const selectedPubLangs = pubDef?.lstLanguages;
        // console.log("setting langs", pubLangs, selectedPubLangs);
        // set name for picker
        pubLangs.forEach((lang) => {
            lang.name = lang.description;
        });
        if (selectedPubLangs) {
            selectedPubLangs.forEach((lang) => {
                lang.name = lang.description;
            });
        }
        this.pubLangs = pubLangs;
        if (pubDef) {
            this.selectedPubLangs = selectedPubLangs;
            this.pubLangs = pubLangs.filter((lang) => !selectedPubLangs.some((dto) => dto.langId === lang.langId));
        }
    }
    // the engine id needs to be added for each output... pubOutputs seems to have the ID, but
    async setPubOutputs() {
        const pubDef = this.dialogOpenData?.pubDef;
        const pubOutputs = await wexlib.getPubEngineOutputs();
        // const selectedPubOutputs = pubDef?.lstSelectedOutputFormats.map(
        //   (engine) => {
        //     const test = engine.lstPublishOutputs[0];
        //     console.log("inside map: engine", engine);
        //     console.log("inside map: test", test);
        //     return test;
        //   }
        // );
        const selectedPubOutputs = pubDef?.lstOutputFormats;
        console.log("setPubOutputs: pubDef", pubDef);
        console.log("setPubOutputs: pubOutputs", pubOutputs);
        console.log("setPubOutputs: selectedPubOutputs", selectedPubOutputs);
        this.pubOutputs = pubOutputs;
        if (pubDef) {
            this.selectedPubOutputs = selectedPubOutputs;
            this.pubOutputs = pubOutputs.filter((output) => !selectedPubOutputs.some((dto) => dto.outputTypeId === output.outputTypeId));
        }
    }
    async init() {
        this.dialogOpenData = this.state?.publish_defs_create_dialog_open;
        this.stage = this.initialStage;
        this.selectedProjectId = -1;
        this.selectedPubContent = [];
        this.selectedPubLangs = [];
        this.selectedPubOutputs = [];
        this._name = "";
        this._desc = "";
    }
    async handleProjectSelect(type = null, e) {
        // console.log("project selection changed");
        try {
            if (!type)
                return;
            if (type == "project") {
                const id = e.detail.selectedOption.value;
                this.selectedProjectId = id;
                this._errors.project = null;
            }
            // console.log("selected project:", this.selectedProjectId);
        }
        catch { }
    }
    handleChange(type, e) {
        console.log("handleChange: type", type);
        console.log("handleChange: e", e);
        if (!type)
            return;
        const target = e.target;
        const value = target?.value ?? "";
        console.log("handleChange: value", value);
        // this[`_${type}`] = value;
        if (!value.length) {
            this._errors[type] = this.string["_error_empty_value"];
        }
        else if (!this.isValidString(value)) {
            this._errors[type] = this.string["_error_invalid_value"];
        }
        else {
            this._errors[type] = null;
        }
        if (type === "name")
            this._name = value;
        if (type === "desc")
            this._desc = value;
        this.requestUpdate();
    }
    isValidString(str) {
        if (!str)
            return false;
        const rg1 = /^[^\\/:\*\?"<>+%@#&,=~'\|]+$/; // forbidden characters \ / : * ? " < > |
        const rg2 = /^[^\.]/; // cannot start with dot (.)
        const rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\.|$)/i; // forbidden folder names
        return rg1.test(str) && rg2.test(str) && !rg3.test(str);
    }
    renderBasicInfoStage() {
        const template = html `
      <wex-col-item>
        <wex-col-item justifyContent="flex-start">
          <ui5-label required>${this.string["_name"]}</ui5-label>
          <ui5-input
            value="${this._name}"
            @input="${(e) => this.handleChange("name", e)}"
          >
          </ui5-input>
          ${!!this._errors.name
            ? html ` <span class="error-msg">${this._errors.name}</span> `
            : html ``}
        </wex-col-item>

        <wex-col-item justifyContent="flex-start">
          <ui5-label required>${this.string["_description"]}</ui5-label>
          <ui5-input
            value="${this._desc}"
            @input="${(e) => this.handleChange("desc", e)}"
          >
          </ui5-input>
          ${!!this._errors.desc
            ? html ` <span class="error-msg">${this._errors.desc}</span> `
            : html ``}
        </wex-col-item>

        <wex-col-item justifyContent="flex-start">
          <ui5-label required>${this.string["_project"]}</ui5-label>

          <ui5-select
            @change="${this.handleProjectSelect.bind(this, "project")}"
          >
            <ui5-option value="-1"> No project selected </ui5-option>
            ${this.dialogOpenData && this.dialogOpenData.projects
            ? this.dialogOpenData.projects.map((project) => html `
                    <ui5-option
                      ?selected=${this.selectedProjectId == project.projectId}
                      value="${project.projectId}"
                    >
                      ${project.name}
                    </ui5-option>
                  `)
            : null}
          </ui5-select>
          ${!!this._errors.project
            ? html ` <span class="error-msg">${this._errors.project}</span> `
            : html ``}
        </wex-col-item>
      </wex-col-item>
    `;
        return template;
    }
    renderTablePicker(stage, selected, unselected) {
        const template = html `
      <wex-col-item>
        <wex-table-picker
          id="pubdef-${stage}"
          .selected=${selected}
          .unselected=${unselected}
          @pick-list-updated=${(e) => this.handlePickListUpdated(e, stage)}
        ></wex-table-picker>

        ${!!this._errors.selected
            ? html ` <span class="error-msg">${this._errors.selected}</span> `
            : html ``}
      </wex-col-item>
    `;
        return template;
    }
    renderListPicker(stage, selected, unselected) {
        // console.log("renderListPicker", stage, selected, unselected);
        const template = html `
      <wex-col-item>
        <wex-list-picker
          id="pubdef-${stage}"
          .selected=${selected}
          .unselected=${unselected}
          @pick-list-updated=${(e) => this.handlePickListUpdated(e, stage)}
        ></wex-list-picker>

        ${!!this._errors.selected
            ? html ` <span class="error-msg">${this._errors.selected}</span> `
            : html ``}
      </wex-col-item>
    `;
        return template;
    }
    renderContentStage() {
        console.log("renderContentStage: this.selectedPubContent", this.selectedPubContent);
        return this.renderTablePicker("content", this.selectedPubContent, this.pubContent);
    }
    renderLanguagesStage() {
        return this.renderListPicker("languages", this.selectedPubLangs, this.pubLangs);
    }
    renderOutputsStage() {
        return this.renderListPicker("outputs", this.selectedPubOutputs, this.pubOutputs);
    }
    log() {
        const body = {
            name: this._name,
            desc: this._desc,
            projectId: this.selectedProjectId,
            pubContent: this.selectedPubContent,
            pubLangs: this.selectedPubLangs,
            pubOutputs: this.selectedPubOutputs,
            pubDefId: -1,
        };
        body.pubDefId = this.dialogOpenData?.pubDef.pubDefId;
        console.log("log: body", body);
    }
    renderFilenameStage() {
        // <ui5-label>Filename Stage</ui5-label>
        // <ui5-input value="${this._name}" />
        const template = html `
      <div>
        <ui5-button @click=${this.log}>Log</ui5-button>
        <p>Click the log button</p>
      </div>
    `;
        return template;
    }
    renderBody() {
        switch (this.stage) {
            case "basic":
                return this.renderBasicInfoStage();
            case "content":
                return this.renderContentStage();
            case "languages":
                return this.renderLanguagesStage();
            case "outputs":
                return this.renderOutputsStage();
            case "filename":
                return this.renderFilenameStage();
            default:
                return html ` <div>Error, ${this.stage}</div> `;
        }
    }
    render() {
        const template = html `
      <wex-wizard-dialog
        .open=${!!this.dialogOpenData}
        headerText="Publish Definition"
        mode=${this.dialogOpenData?.mode || ""}
        width="50%"
        height="70vh"
        .stages=${this.stages}
        initialStage="basic"
        dialogOpenStateProperty="publish_defs_create_dialog_open"
        @set-wizard-stage=${(e) => (this.stage = e.detail)}
      >
        ${this.renderBody()}
      </wex-wizard-dialog>
    `;
        return template;
    }
};
__decorate([
    property({ type: Object })
], WexDialogPublishDefsCreate.prototype, "dialogOpenData", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "state", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "string", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "stage", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "pubContent", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "pubLangs", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "pubOutputs", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedPubContent", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedPubLangs", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedPubOutputs", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "pubDef", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "pubDefId", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedProject", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedProjectId", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedProjectName", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "selectedProjectDesc", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "_name", void 0);
__decorate([
    state()
], WexDialogPublishDefsCreate.prototype, "_desc", void 0);
WexDialogPublishDefsCreate = __decorate([
    customElement("wex-dialog-publish-defs-create")
], WexDialogPublishDefsCreate);
export { WexDialogPublishDefsCreate };
//# sourceMappingURL=publish-defs-create.js.map