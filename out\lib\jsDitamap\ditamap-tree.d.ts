import { FileMeta } from "@bds/types";
import { DitamapNode } from "./ditamap-node";
export declare class DitamapTree {
    /**
     * Context containing workspace, metadata, and other shared state
     */
    context: DitamapContext;
    /**
     * Underlying tree data structure
     */
    root: DitamapNode | null;
    /**
     * Name of the root map
     */
    rootMapName: string;
    constructor(rootMapName: string, files?: Map<string, HTMLElement>);
    getRoot(): DitamapNode | null;
    load(): Promise<void>;
    rebuildTree(): Promise<void>;
    buildTree(mapName: string): Promise<DitamapNode | null>;
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml: HTMLElement): Promise<DitamapNode>;
    _addToMetadataMap(href: string): void;
    /** Adds ids to xml which will be stable accross tree operations */
    _addIdsToXml(rootEl: HTMLElement): void;
    _getStructuralElement(href: string): Promise<HTMLElement | undefined>;
    /** -- Fetch calls -- */
    _fetchMetadata(filenames: string): Promise<void>;
    _fetchMap(reslblId: string): Promise<void>;
}
export interface DitamapContext {
    workspace: Map<string, HTMLElement>;
    metadata: Map<string, FileMeta | null>;
    dirty: Set<string>;
    cutId: string | null;
    buffer: DitamapNode | null;
}
/**
 *
 * The traversal should not include map or bookmap elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
