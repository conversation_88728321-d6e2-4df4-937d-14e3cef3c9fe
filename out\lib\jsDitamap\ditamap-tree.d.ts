import { DitamapNode } from "./ditamap-node";
export declare class DitamapTree {
    /**
     * Map of all the stored ditamap xml
     */
    workspace: Map<string, HTMLElement>;
    /**
     * Underlying tree data structure
     */
    root: DitamapNode | null;
    /**
     * Name of the root map
     */
    rootMapName: string;
    constructor(rootMapName: string, files?: Map<string, HTMLElement>);
    buildTree(mapName: string): DitamapNode | null;
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml: HTMLElement): DitamapNode;
    /**
     * Checks if an html element is a mapref or part
     * @param element
     */
    static isMapRefLike(element: HTMLElement): boolean;
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isContent(element: HTMLElement): boolean;
    /**
     * Gets the href used to identify the map, if no href assume it is the root map
     * @param element - xml element
     * @param rootMapName - name of the root map
     */
    static getMapName(element: HTMLElement, rootMapName?: string): string | undefined;
    static getMapTitle(element: HTMLElement): string;
    static removeXmlNoise(str: string): string;
}
/**
 *
 * The traversal should not include any MapLike elements see diagram: https://lucid.app/lucidchart/ce928ebd-d0ea-4f56-bdcd-9ed2fd4990dd/edit?invitationId=inv_97faf72a-9228-4c23-b7fe-8ade2361e9f5&page=0_0#
 */
//# sourceMappingURL=ditamap-tree.d.ts.map