import { DitamapTree } from "./ditamap-tree";
export class RootMapNode {
    constructor(rootElement, rootMapName) {
        this.mapPath = [];
        this.children = [];
        this.href = rootMapName;
        this.containingMap = null;
        this.refElement = null;
        this.structuralElement = rootElement;
    }
    get title() {
        return DitamapTree.getMapTitle(this.structuralElement);
    }
    get tagName() {
        return this.structuralElement.tagName;
    }
}
export class MapRefLikeElementNode {
    constructor(refElement, structuralElement, containingMap) {
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.structuralElement = structuralElement;
    }
    get title() {
        return DitamapTree.getMapTitle(this.refElement);
    }
    get tagName() {
        return this.refElement.tagName;
    }
}
/**
 * Content elements reference content and provide structure.
 */
export class ContentNode {
    constructor(refElement, containingMap) {
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.structuralElement = refElement;
    }
    get title() {
        return DitamapTree.getMapTitle(this.refElement);
    }
    get tagName() {
        return this.refElement.tagName;
    }
}
//# sourceMappingURL=ditamap-node.js.map