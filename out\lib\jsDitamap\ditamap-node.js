import { DitamapUtils } from "./ditamap-utils";
export class RootMapNode {
    constructor(rootElement, rootMapName) {
        this.mapPath = [];
        this.children = [];
        this.href = rootMapName;
        this.containingMap = null;
        this.refElement = null;
        this.structuralElement = rootElement;
        this.title = DitamapUtils.getMapTitle(this.structuralElement);
    }
}
export class MapRefLikeElementNode {
    constructor(refElement, structuralElement, containingMap) {
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.structuralElement = structuralElement;
        this.title = DitamapUtils.getMapTitle(this.structuralElement);
    }
}
/**
 * Content elements reference content and provide structure.
 */
export class ContentNode {
    constructor(refElement, containingMap) {
        this.mapPath = [];
        this.children = [];
        this.href = refElement.getAttribute("href");
        this.containingMap = containingMap;
        this.refElement = refElement;
        this.structuralElement = refElement;
        this.title = DitamapUtils.getMapTitle(this.refElement);
    }
}
//# sourceMappingURL=ditamap-node.js.map