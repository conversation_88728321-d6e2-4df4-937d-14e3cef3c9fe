var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit-element";
import { customElement, property, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// @ts-ignore
import * as wexlib from "lib/wexlib.js";
import * as util from "lib/util.js";
import "@ui5/webcomponents/dist/Button.js";
import "../base/col-item";
import "../base/row-item";
import "../base/icon";
let WexPublishDefinitionsActionBar = class WexPublishDefinitionsActionBar extends LitElement {
    constructor() {
        super(...arguments);
        this.projects = [];
        this.pubDefs = [];
        this._string = null;
        this._handleSelectAllRows = () => {
            this.dispatchEvent(new CustomEvent("select-all-definitions", {
                bubbles: true,
                composed: true,
            }));
        };
        this._handleClearSelectedRows = () => {
            this.dispatchEvent(new CustomEvent("clear-selected-definitions", {
                bubbles: true,
                composed: true,
            }));
        };
    }
    static get styles() {
        return css ``;
    }
    async _editPubDef() {
        console.log("***********************");
        const selectedPubDefId = this.pubDefs.find((pubDef) => pubDef.selected).pubDefId; // need to get from API to ensure correct data
        const selectedPubDef = await wexlib.getPubDef(selectedPubDefId);
        // need to toast if multiples are selected; it only supports a single pubdef selection
        console.log("selectedPubDef Array:", selectedPubDef);
        console.log("pubDef to edit", selectedPubDef);
        // console.log("fresh data:", selectedPubDefTest);
        storeInstance.dispatch("setState", {
            property: "publish_defs_create_dialog_open",
            value: {
                mode: "Edit",
                projects: this.projects,
                pubDef: selectedPubDef,
            },
        });
    }
    async _runPubDef() {
        try {
            const selected = this.pubDefs.filter((pubDef) => pubDef.selected);
            // bail out if anything is not totally complete
            // TODO Xdocs issue with sql tables adding unremovable items
            await wexlib.runPubDefs(selected);
            util.notifyUser("positive", this._string["_msg_pubish_run_success"]);
        }
        catch {
        }
        finally {
        }
    }
    async _deletePubDef() {
        // console.log("delete pubdef with id", this._selectedPubDefId);
        try {
            const selected = this.pubDefs.filter((pubDef) => pubDef.selected);
            console.log("delete this:", selected);
            await wexlib.rmPubDef(selected[0].pubDefId);
            const pubDefs = await wexlib.getAllPubDefs();
            console.log("pubDefs after delete", pubDefs);
            if (pubDefs.length) {
                console.log("settings pubdefs", pubDefs);
                storeInstance.dispatch("setPubDefs", {
                    pubDefs,
                });
                storeInstance.dispatch("touch"); // force state update
            }
        }
        catch (e) {
            console.error("delete", e);
        }
    }
    render() {
        return html `
      <wex-row-item height="42px">
        <wex-col-item>
          <wex-row-item justifyContent="flex-start" alignItems="center">
            <wex-icon
              icon="icons:clear"
              class="pointer action-icon"
              title="Clear selection"
              @click="${this._handleClearSelectedRows}"
            ></wex-icon>
            <ui5-button @click="${this._handleSelectAllRows}"
              >Select all</ui5-button
            >
          </wex-row-item>
        </wex-col-item>

        <wex-col-item>
          <wex-row-item justifyContent="flex-end" alignItems="center">
            <ui5-button @click=${this._editPubDef.bind(this)}>Edit</ui5-button>
            <ui5-button @click="${this._runPubDef.bind(this)}">Run</ui5-button>
            <ui5-button
              @click="${this._deletePubDef.bind(this)}"
              design="Negative"
              >Delete</ui5-button
            >
          </wex-row-item>
        </wex-col-item>
      </wex-row-item>
    `;
    }
};
__decorate([
    property({ type: Array })
], WexPublishDefinitionsActionBar.prototype, "projects", void 0);
__decorate([
    property({ type: Array })
], WexPublishDefinitionsActionBar.prototype, "pubDefs", void 0);
__decorate([
    state()
], WexPublishDefinitionsActionBar.prototype, "_string", void 0);
WexPublishDefinitionsActionBar = __decorate([
    customElement("wex-publish-definitions-action-bar")
], WexPublishDefinitionsActionBar);
export { WexPublishDefinitionsActionBar };
//# sourceMappingURL=definitions-action-bar.js.map