{"version": 3, "file": "browse-publish.js", "sourceRoot": "", "sources": ["../../../src/components/dialog/browse-publish.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,qBAAqB,CAAC;AAC9C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAmDnB,IAAM,sBAAsB,GAA5B,MAAM,sBAAuB,SAAQ,UAAU;IAA/C;;QACuB,mBAAc,GAA0B,IAAI,CAAC;QAC7C,UAAK,GAAQ,EAAE,CAAC;QAChB,WAAM,GAAQ,EAAE,CAAC;QAEpC,wBAAmB,GAA+B,IAAI,CAAC;QACvD,sBAAiB,GAA6B,IAAI,CAAC;QACnD,iBAAY,GAAwB,IAAI,CAAC,CAAC,uCAAuC;QAElF,iBAAY,GAAG,IAAI,CAAC;QACpB,yBAAoB,GAA0B,EAAE,CAAC;QACjD,uBAAkB,GAAwB,EAAE,CAAC;QAC7C,kBAAa,GAAmB,EAAE,CAAC;QACnC,cAAS,GAAW,EAAE,CAAC;QACvB,mBAAc,GAAW,EAAE,CAAC;QAC5B,iBAAY,GAAwB,IAAI,CAAC;QA8LzC,YAAO,GAAG,GAAG,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAEnD,IAAI,CAAC;gBACH,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC;gBAChC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC;gBACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC;gBAC9D,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,YAAY,CAAC;gBAChE,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC;gBAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC;gBACrD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;gBAE3C,MAAM,MAAM,GAA2B;oBACrC,oBAAoB,EAAE,GAAG,oBAAoB,EAAE;oBAC/C,gBAAgB,EAAE,GAAG,gBAAgB,IAAI,EAAE,EAAE;oBAC7C,gBAAgB,EAAE,GAAG,gBAAgB,IAAI,EAAE,EAAE;oBAC7C,eAAe,EAAE,GAAG,eAAe,IAAI,EAAE,EAAE;oBAC3C,cAAc,EAAE,GAAG,cAAc,IAAI,EAAE,EAAE;oBACzC,YAAY,EAAE,GAAG,YAAY,IAAI,EAAE,EAAE;oBACrC,WAAW,EAAE,GAAG,WAAW,IAAI,EAAE,EAAE;iBACpC,CAAC;gBAEF,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEzC,+BAA+B;gBAE/B,MAAM,GAAG,GAAG,KAAK,CAAC,4BAA4B,EAAE;oBAC9C,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE;wBACP,cAAc,EAAE,mCAAmC;qBACpD;oBACD,IAAI;iBACL,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IA3NQ,iBAAiB;QACtB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEM,oBAAoB;QACzB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAES,OAAO,CAAC,iBAAsB;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,YAAY,CAAQ,CAAC;QACnE,IACE,MAAM;YACN,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACvC,IAAI,CAAC,cAAc,EACnB,CAAC;YACD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,+BAA+B;IAC1F,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,qBAAqB,EAAE,CAAC;QACjD,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C;IACnG,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;IAC3B,CAAC;IAEO,WAAW,CAAC,KAAU;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,0BAA0B,CAAC;QAEvD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC;IACjD,CAAC;IAEO,iBAAiB,CAAC,CAAa;QACrC,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,yCAAyC;IAC/E,CAAC;IAEO,UAAU;QAChB,4CAA4C;QAE5C,OAAO,IAAI,CAAA;;;;;gCAKiB,IAAI,CAAC,YAAY,EAAE,IAAI;;;;;;;;;qBASlC,IAAI,CAAC,cAAc;sBAClB,CAAC,CAAa,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;;;;;;;;;wBAS1C,IAAI,CAAC,aAAa;+BACX,IAAI,CAAC,YAAY;kCACd,CAAC,CAAc,EAAE,EAAE,CACvC,IAAI,CAAC,qBAAqB,CACxB,CAAC,CAAC,MAAM,CAAC,cAAc,EACvB,cAAc,CACf;;;;;;;;;gCASiB,KAAK;wBACb,IAAI,CAAC,oBAAoB;+BAClB,IAAI,CAAC,mBAAmB;kCACrB,CAAC,CAAc,EAAE,EAAE,CACvC,IAAI,CAAC,qBAAqB,CACxB,CAAC,CAAC,MAAM,CAAC,cAAc,EACvB,qBAAqB,CACtB;;;;;;;;;gCASiB,KAAK;wBACb,IAAI,CAAC,kBAAkB;+BAChB,IAAI,CAAC,iBAAiB;kCACnB,CAAC,CAAc,EAAE,EAAE,CACvC,IAAI,CAAC,qBAAqB,CACxB,CAAC,CAAC,MAAM,CAAC,cAAc,EACvB,mBAAmB,CACpB;;;;KAIV,CAAC;IACJ,CAAC;IAES,MAAM;QACd,sEAAsE;QACtE,OAAO,IAAI,CAAA;;;kCAGmB,4BAA4B;4BAClC,CAAC,IAAI,CAAC,YAAY;iBAC7B,CAAC,CAAC,IAAI,CAAC,cAAc;sBAChB,IAAI,CAAC,OAAO;;;;;UAKxB,IAAI,CAAC,UAAU,EAAE;;KAEtB,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,cAAmB,EAAE,QAAgB;QACjE,yCAAyC;QACzC,qDAAqD;QACrD,qBAAqB;QACrB,iEAAiE;QACjE,OAAO;QACP,IAAI;QAEH,IAAY,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC;QAEzC,qDAAqD;QACrD,IAAI,QAAQ,KAAK,cAAc,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,SAAS,GAAG,cAAc,EAAE,SAAS,IAAI,EAAE,CAAC;YACjD,IAAI,CAAC,cAAc;gBACjB,cAAc,IAAI,IAAI,CAAC,YAAY;oBACjC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS;oBACvD,CAAC,CAAC,EAAE,CAAC;QACX,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7E,CAAC;;AA1LM,6BAAM,GAAG,GAAG,CAAA;;;;;;;;;;;;;GAalB,AAbY,CAaX;AA7B0B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8DAA8C;AAC7C;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;qDAAiB;AAChB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;sDAAkB;AAEpC;IAAR,KAAK,EAAE;mEAAwD;AACvD;IAAR,KAAK,EAAE;iEAAoD;AACnD;IAAR,KAAK,EAAE;4DAA0C;AAPvC,sBAAsB;IADlC,aAAa,CAAC,2BAA2B,CAAC;GAC9B,sBAAsB,CA2PlC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"../../lib/wexlib.js\";\r\nimport \"../base/col-item\";\r\nimport \"../common/dialog\";\r\nimport \"../common/select\";\r\nimport \"../base/row-item\";\r\n\r\ninterface DialogOpenData {\r\n  //   activeProcesses: null;\r\n  //   ditaClass: string;\r\n  //   fileId: string;\r\n  //   fileStatus: null;\r\n  //   folderPath: string;\r\n  //   hitText: string;\r\n  //   iconClass: string;\r\n  //   iconTitle: string;\r\n  //   isXml: true;\r\n  //   lcname: string;\r\n  //   lctitle: string;\r\n  //   lineageId: number;\r\n  //   lockDate: undefined;\r\n  //   lockLocation: undefined;\r\n  //   lockOwner: null;\r\n  //   mapClass: string;\r\n  //   mimeType: string;\r\n  //   name: string;\r\n  //   numConnections: string;\r\n  //   resLblId: string;\r\n  //   resPathId: string;\r\n  //   rootElementName: string;\r\n  //   selected: string;\r\n  //   size: string;\r\n  //   status: string;\r\n  //   title: string;\r\n  //   verCreateDate: string;\r\n}\r\n\r\ninterface SelectedFile {\r\n  name: string;\r\n  lineageId: string;\r\n}\r\n\r\ninterface PresentationProfile {\r\n  profileLblId: string;\r\n}\r\n\r\ninterface ProcessingProfile {\r\n  profileLblId: string;\r\n}\r\n\r\ninterface OutputFormat {\r\n  publishEngineId: string;\r\n  outputTypeId: string;\r\n}\r\n\r\n@customElement(\"wex-dialog-browse-publish\")\r\nexport class WexDialogBrowsePublish extends LitElement {\r\n  @property({ type: Object }) dialogOpenData: DialogOpenData | null = null;\r\n  @property({ type: Object }) state: any = {};\r\n  @property({ type: Object }) string: any = {};\r\n\r\n  @state() presentationProfile: PresentationProfile | null = null;\r\n  @state() processingProfile: ProcessingProfile | null = null;\r\n  @state() outputFormat: OutputFormat | null = null; // falsy value to disable submit button\r\n\r\n  private subscription = null;\r\n  private presentationProfiles: PresentationProfile[] = [];\r\n  private processingProfiles: ProcessingProfile[] = [];\r\n  private outputFormats: OutputFormat[] = [];\r\n  private outputExt: string = \"\";\r\n  private outputFileName: string = \"\";\r\n  private selectedFile: SelectedFile | null = null;\r\n\r\n  static styles = css`\r\n    ui5-input {\r\n      width: 100%;\r\n    }\r\n    .more-item {\r\n      box-sizing: border-box;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0 1rem;\r\n      height: 2.25rem;\r\n      border: 1px solid var(--clr-gray-light);\r\n    }\r\n  `;\r\n\r\n  public connectedCallback() {\r\n    super.connectedCallback();\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.setPresentationProfiles();\r\n    this.setProcessingProfiles();\r\n    this.setOutputFormats();\r\n  }\r\n\r\n  public disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  protected updated(changedProperties: any) {\r\n    const dialog = this.shadowRoot?.querySelector(\"ui5-dialog\") as any;\r\n    if (\r\n      dialog &&\r\n      changedProperties.has(\"dialogOpenData\") &&\r\n      this.dialogOpenData\r\n    ) {\r\n      dialog.open = true;\r\n    }\r\n  }\r\n\r\n  private async setPresentationProfiles() {\r\n    const res = await wexlib.getPresentationProfiles();\r\n    this.presentationProfiles = res;\r\n    this.presentationProfile = this.presentationProfiles[0]; // also set the current profile\r\n  }\r\n\r\n  private async setProcessingProfiles() {\r\n    const res = await wexlib.getProcessingProfiles();\r\n    this.processingProfiles = res;\r\n    this.processingProfile = this.processingProfiles[0]; // set default here now because it's defined\r\n  }\r\n\r\n  private async setOutputFormats() {\r\n    const res = await wexlib.getPubEngineOutputs();\r\n    this.outputFormats = res;\r\n  }\r\n\r\n  private stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.dialogOpenData = state.browse_publish_dialog_open;\r\n\r\n    this.selectedFile = state.browse_selected_file;\r\n  }\r\n\r\n  private handleInputChange(e: InputEvent) {\r\n    const target = e.target as HTMLInputElement;\r\n    this.outputFileName = target.value; // update outputFileName with input value\r\n  }\r\n\r\n  private renderBody() {\r\n    // <h3>${this._string[\"_project_name\"]}</h3>\r\n\r\n    return html`\r\n      <wex-row-item>\r\n        <wex-col-item>\r\n          <h3>Map</h3>\r\n          <div class=\"more-item\">\r\n            <span title=\"Map\">${this.selectedFile?.name}</span>\r\n          </div>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n\r\n      <wex-row-item>\r\n        <wex-col-item>\r\n          <h3>Output File Name</h3>\r\n          <ui5-input\r\n            value=\"${this.outputFileName}\"\r\n            @input=\"${(e: InputEvent) => this.handleInputChange(e)}\"\r\n          ></ui5-input>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n\r\n      <wex-row-item>\r\n        <wex-col-item>\r\n          <h3>Output Format</h3>\r\n          <wex-select\r\n            .options=\"${this.outputFormats}\"\r\n            .selectedOption=\"${this.outputFormat}\"\r\n            @selection-changed=\"${(e: CustomEvent) =>\r\n              this.handleSelectionChange(\r\n                e.detail.selectedOption,\r\n                \"outputFormat\"\r\n              )}\"\r\n          ></wex-select>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n\r\n      <wex-row-item>\r\n        <wex-col-item>\r\n          <h3>Presentation Profile</h3>\r\n          <wex-select\r\n            .showPlaceholder=\"${false}\"\r\n            .options=\"${this.presentationProfiles}\"\r\n            .selectedOption=\"${this.presentationProfile}\"\r\n            @selection-changed=\"${(e: CustomEvent) =>\r\n              this.handleSelectionChange(\r\n                e.detail.selectedOption,\r\n                \"presentationProfile\"\r\n              )}\"\r\n          ></wex-select>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n\r\n      <wex-row-item>\r\n        <wex-col-item>\r\n          <h3>Processing Profile</h3>\r\n          <wex-select\r\n            .showPlaceholder=\"${false}\"\r\n            .options=\"${this.processingProfiles}\"\r\n            .selectedOption=\"${this.processingProfile}\"\r\n            @selection-changed=\"${(e: CustomEvent) =>\r\n              this.handleSelectionChange(\r\n                e.detail.selectedOption,\r\n                \"processingProfile\"\r\n              )}\"\r\n          ></wex-select>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  protected render() {\r\n    // <ui5-dialog header-text=\"${this.string[\"_browse_publish_dialog\"]}\">\r\n    return html`\r\n      <wex-dialog\r\n        headerText=\"Run Adhoc Publishing Job\"\r\n        dialogOpenStateProperty=${\"browse_publish_dialog_open\"}\r\n        .confirmDisabled=\"${!this.outputFormat}\"\r\n        .open=\"${!!this.dialogOpenData}\"\r\n        .onConfirm=\"${this.publish}\"\r\n        confirmLabel=\"Publish\"\r\n        cancelLabel=\"Cancel\"\r\n        width=\"40%\"\r\n      >\r\n        ${this.renderBody()}\r\n      </wex-dialog>\r\n    `;\r\n  }\r\n\r\n  private handleSelectionChange(selectedOption: any, property: string) {\r\n    // this check is broken and seems useless\r\n    // if (!Object.hasOwnProperty.call(this, property)) {\r\n    //   throw new Error(\r\n    //     `Property \"${property}\" does not exist on this component.`\r\n    //   );\r\n    // }\r\n\r\n    (this as any)[property] = selectedOption;\r\n\r\n    // If outputFormat was changed, update filename parts\r\n    if (property === \"outputFormat\") {\r\n      console.log(\"handleSelectionChange: outputFormat\", selectedOption);\r\n      this.outputExt = selectedOption?.outputExt || \"\";\r\n      this.outputFileName =\r\n        selectedOption && this.selectedFile\r\n          ? this.selectedFile.name.split(\".\")[0] + this.outputExt\r\n          : \"\";\r\n    }\r\n\r\n    this.requestUpdate();\r\n    console.log(\"handleSelectionChange: this.outputFormat\", this.outputFormat);\r\n  }\r\n\r\n  private publish = () => {\r\n    console.log(\"publish\");\r\n    console.log(\"publication\", this.selectedFile);\r\n    console.log(\"outputFormat\", this.outputFormat);\r\n    console.log(\"presentationProfile\", this.presentationProfile);\r\n    console.log(\"processingProfile\", this.processingProfile);\r\n    console.log(\"outputFileName\", this.outputFileName);\r\n\r\n    try {\r\n      const publicationContentId = -1;\r\n      const resLblIdMap = this.selectedFile?.lineageId;\r\n      const procProfileLblId = this.processingProfile?.profileLblId;\r\n      const presProfileLblId = this.presentationProfile?.profileLblId;\r\n      const publishEngineId = this.outputFormat?.publishEngineId;\r\n      const outputTypeId = this.outputFormat?.outputTypeId;\r\n      const outputFileName = this.outputFileName;\r\n\r\n      const params: Record<string, string> = {\r\n        publicationContentId: `${publicationContentId}`,\r\n        procProfileLblId: `${procProfileLblId ?? \"\"}`,\r\n        presProfileLblId: `${presProfileLblId ?? \"\"}`,\r\n        publishEngineId: `${publishEngineId ?? \"\"}`,\r\n        outputFileName: `${outputFileName ?? \"\"}`,\r\n        outputTypeId: `${outputTypeId ?? \"\"}`,\r\n        resLblIdMap: `${resLblIdMap ?? \"\"}`,\r\n      };\r\n\r\n      const body = new URLSearchParams(params);\r\n\r\n      //   console.log(\"body\", body);\r\n\r\n      const res = fetch(\"/lwa/jrest/RunAdHocPublish\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n        },\r\n        body,\r\n      });\r\n\r\n      return true;\r\n    } catch (e) {\r\n      console.error(e);\r\n    } finally {\r\n      this.outputFileName = \"\";\r\n    }\r\n  };\r\n}\r\n"]}