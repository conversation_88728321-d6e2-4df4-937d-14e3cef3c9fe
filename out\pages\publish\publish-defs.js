var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, state } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// @ts-ignore
import * as wexlib from "lib/wexlib.js";
import "@vaadin/vaadin-split-layout";
import "../../components/base/row-item";
import "../../components/common/mainpane";
import "../../components/publish/publish-definitions-pane";
import "../../components/publish/definition-view-pane";
import "../../components/header/publish";
// TODO -- add to initial state
// const DEBOUNCE_DURATION = 1000;
let WexPagePublishDefs = class WexPagePublishDefs extends LitElement {
    constructor() {
        super(...arguments);
        this.selectedProjectId = null;
        this.selectedPubDefId = null;
        this.state = storeInstance.state;
        this.string = [];
    }
    static get styles() {
        return css `
      #publish-defs-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        max-height: 100%;
      }
      .mainpane {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        justify-content: space-evenly;
        justify-content: center;
        color: var(--font-color);
        height: 100%;
        max-height: 100%;
      }
      .mainpane > *:not(:last-child) {
        margin-bottom: 1rem;
        border: 1px solid #f00;
      }
      .split-layout {
        width: 100%;
        height: 100%;
      }
      .right,
      .left {
        padding: 1rem;
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this._init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
    }
    async _init() {
        try {
            const categories = await wexlib.getProjCategories();
            if (categories.length) {
                storeInstance.dispatch("setPublishCategories", {
                    categories,
                });
            }
            const projects = await wexlib.getProjectsByProjCategoryId();
            if (projects.length) {
                storeInstance.dispatch("setPublishProjects", {
                    projects,
                });
            }
            const pubDefs = await wexlib.getAllPubDefs();
            if (pubDefs.length) {
                storeInstance.dispatch("setPubDefs", {
                    pubDefs,
                });
            }
        }
        catch (error) {
            console.error("Error initializing publish definitions:", error);
        }
    }
    _handleSinglePubDefSelected(e) {
        this.selectedProjectId = e.detail.projectId;
        this.selectedPubDefId = e.detail.pubDefId;
    }
    _handleResetSelectedPubDef() {
        this.selectedProjectId = null;
        this.selectedPubDefId = null;
    }
    _renderPubDefsPane() {
        const template = html `
      <wex-publish-definitions-pane
        class="left"
        @reset-selected-pubdef=${this._handleResetSelectedPubDef.bind(this)}
        @single-pubdef-selected=${this._handleSinglePubDefSelected.bind(this)}
      ></wex-publish-definitions-pane>
    `;
        return template;
    }
    _renderDefViewPane() {
        const template = html `
      <wex-definition-view-pane
        class="right"
        .selectedProjectId=${this.selectedProjectId}
        .selectedPubDefId=${this.selectedPubDefId}
      ></wex-definition-view-pane>
    `;
        return template;
    }
    _renderMainPane() {
        // <wex-row-item>
        const template = html `
      <section class="mainpane">
        <vaadin-split-layout class="split-layout">
          ${this._renderPubDefsPane()} ${this._renderDefViewPane()}
        </vaadin-split-layout>
      </section>
    `;
        // </wex-row-item>
        return template;
    }
    render() {
        return html `
      <div id="publish-defs-container">
        <wex-publish-header></wex-publish-header>
        ${this._renderMainPane()}
      </div>
    `;
    }
};
__decorate([
    state()
], WexPagePublishDefs.prototype, "selectedProjectId", void 0);
__decorate([
    state()
], WexPagePublishDefs.prototype, "selectedPubDefId", void 0);
WexPagePublishDefs = __decorate([
    customElement("wex-page-publish-defs")
], WexPagePublishDefs);
export { WexPagePublishDefs };
//# sourceMappingURL=publish-defs.js.map